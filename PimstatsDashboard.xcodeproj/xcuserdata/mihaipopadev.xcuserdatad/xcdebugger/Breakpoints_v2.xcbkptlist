<?xml version="1.0" encoding="UTF-8"?>
<Bucket
   uuid = "B319A638-2646-45ED-BAA2-07146D368923"
   type = "1"
   version = "2.0">
   <Breakpoints>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "C94D0788-F2D9-4771-AAD8-42E82FD46BA6"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "PimstatsDashboard/Views/FootballBarRaceView.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "273"
            endingLineNumber = "273"
            landmarkName = "resetTicker()"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
   </Breakpoints>
</Bucket>
