// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXContainerItemProxy section */
		8ED9FC7A2DFEC79A00727768 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 8ED9FC612DFEC79800727768 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 8ED9FC682DFEC79900727768;
			remoteInfo = PimstatsDashboard;
		};
		8ED9FC842DFEC79A00727768 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 8ED9FC612DFEC79800727768 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 8ED9FC682DFEC79900727768;
			remoteInfo = PimstatsDashboard;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		8ED9FC692DFEC79900727768 /* PimstatsDashboard.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = PimstatsDashboard.app; sourceTree = BUILT_PRODUCTS_DIR; };
		8ED9FC792DFEC79A00727768 /* PimstatsDashboardTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = PimstatsDashboardTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		8ED9FC832DFEC79A00727768 /* PimstatsDashboardUITests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = PimstatsDashboardUITests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		8ED9FC6B2DFEC79900727768 /* PimstatsDashboard */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = PimstatsDashboard;
			sourceTree = "<group>";
		};
		8ED9FC7C2DFEC79A00727768 /* PimstatsDashboardTests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = PimstatsDashboardTests;
			sourceTree = "<group>";
		};
		8ED9FC862DFEC79A00727768 /* PimstatsDashboardUITests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = PimstatsDashboardUITests;
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		8ED9FC662DFEC79900727768 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		8ED9FC762DFEC79A00727768 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		8ED9FC802DFEC79A00727768 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		8ED9FC602DFEC79800727768 = {
			isa = PBXGroup;
			children = (
				8ED9FC6B2DFEC79900727768 /* PimstatsDashboard */,
				8ED9FC7C2DFEC79A00727768 /* PimstatsDashboardTests */,
				8ED9FC862DFEC79A00727768 /* PimstatsDashboardUITests */,
				8ED9FC6A2DFEC79900727768 /* Products */,
			);
			sourceTree = "<group>";
		};
		8ED9FC6A2DFEC79900727768 /* Products */ = {
			isa = PBXGroup;
			children = (
				8ED9FC692DFEC79900727768 /* PimstatsDashboard.app */,
				8ED9FC792DFEC79A00727768 /* PimstatsDashboardTests.xctest */,
				8ED9FC832DFEC79A00727768 /* PimstatsDashboardUITests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		8ED9FC682DFEC79900727768 /* PimstatsDashboard */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 8ED9FC8D2DFEC79A00727768 /* Build configuration list for PBXNativeTarget "PimstatsDashboard" */;
			buildPhases = (
				8ED9FC652DFEC79900727768 /* Sources */,
				8ED9FC662DFEC79900727768 /* Frameworks */,
				8ED9FC672DFEC79900727768 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				8ED9FC6B2DFEC79900727768 /* PimstatsDashboard */,
			);
			name = PimstatsDashboard;
			packageProductDependencies = (
			);
			productName = PimstatsDashboard;
			productReference = 8ED9FC692DFEC79900727768 /* PimstatsDashboard.app */;
			productType = "com.apple.product-type.application";
		};
		8ED9FC782DFEC79A00727768 /* PimstatsDashboardTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 8ED9FC902DFEC79A00727768 /* Build configuration list for PBXNativeTarget "PimstatsDashboardTests" */;
			buildPhases = (
				8ED9FC752DFEC79A00727768 /* Sources */,
				8ED9FC762DFEC79A00727768 /* Frameworks */,
				8ED9FC772DFEC79A00727768 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				8ED9FC7B2DFEC79A00727768 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				8ED9FC7C2DFEC79A00727768 /* PimstatsDashboardTests */,
			);
			name = PimstatsDashboardTests;
			packageProductDependencies = (
			);
			productName = PimstatsDashboardTests;
			productReference = 8ED9FC792DFEC79A00727768 /* PimstatsDashboardTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		8ED9FC822DFEC79A00727768 /* PimstatsDashboardUITests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 8ED9FC932DFEC79A00727768 /* Build configuration list for PBXNativeTarget "PimstatsDashboardUITests" */;
			buildPhases = (
				8ED9FC7F2DFEC79A00727768 /* Sources */,
				8ED9FC802DFEC79A00727768 /* Frameworks */,
				8ED9FC812DFEC79A00727768 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				8ED9FC852DFEC79A00727768 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				8ED9FC862DFEC79A00727768 /* PimstatsDashboardUITests */,
			);
			name = PimstatsDashboardUITests;
			packageProductDependencies = (
			);
			productName = PimstatsDashboardUITests;
			productReference = 8ED9FC832DFEC79A00727768 /* PimstatsDashboardUITests.xctest */;
			productType = "com.apple.product-type.bundle.ui-testing";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		8ED9FC612DFEC79800727768 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1630;
				LastUpgradeCheck = 1630;
				TargetAttributes = {
					8ED9FC682DFEC79900727768 = {
						CreatedOnToolsVersion = 16.3;
					};
					8ED9FC782DFEC79A00727768 = {
						CreatedOnToolsVersion = 16.3;
						TestTargetID = 8ED9FC682DFEC79900727768;
					};
					8ED9FC822DFEC79A00727768 = {
						CreatedOnToolsVersion = 16.3;
						TestTargetID = 8ED9FC682DFEC79900727768;
					};
				};
			};
			buildConfigurationList = 8ED9FC642DFEC79800727768 /* Build configuration list for PBXProject "PimstatsDashboard" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 8ED9FC602DFEC79800727768;
			minimizedProjectReferenceProxies = 1;
			preferredProjectObjectVersion = 77;
			productRefGroup = 8ED9FC6A2DFEC79900727768 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				8ED9FC682DFEC79900727768 /* PimstatsDashboard */,
				8ED9FC782DFEC79A00727768 /* PimstatsDashboardTests */,
				8ED9FC822DFEC79A00727768 /* PimstatsDashboardUITests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		8ED9FC672DFEC79900727768 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		8ED9FC772DFEC79A00727768 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		8ED9FC812DFEC79A00727768 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		8ED9FC652DFEC79900727768 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		8ED9FC752DFEC79A00727768 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		8ED9FC7F2DFEC79A00727768 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		8ED9FC7B2DFEC79A00727768 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 8ED9FC682DFEC79900727768 /* PimstatsDashboard */;
			targetProxy = 8ED9FC7A2DFEC79A00727768 /* PBXContainerItemProxy */;
		};
		8ED9FC852DFEC79A00727768 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 8ED9FC682DFEC79900727768 /* PimstatsDashboard */;
			targetProxy = 8ED9FC842DFEC79A00727768 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		8ED9FC8B2DFEC79A00727768 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				DEVELOPMENT_TEAM = PMN74SNV3Z;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MACOSX_DEPLOYMENT_TARGET = 15.4;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = macosx;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		8ED9FC8C2DFEC79A00727768 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEVELOPMENT_TEAM = PMN74SNV3Z;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MACOSX_DEPLOYMENT_TARGET = 15.4;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = macosx;
				SWIFT_COMPILATION_MODE = wholemodule;
			};
			name = Release;
		};
		8ED9FC8E2DFEC79A00727768 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = PimstatsDashboard/PimstatsDashboard.entitlements;
				CODE_SIGN_STYLE = Automatic;
				COMBINE_HIDPI_IMAGES = YES;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = PMN74SNV3Z;
				ENABLE_HARDENED_RUNTIME = YES;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
				);
				MACOSX_DEPLOYMENT_TARGET = 14.0;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = mihai.popa.dev.PimstatsDashboard;
				PRODUCT_NAME = "$(TARGET_NAME)";
				REGISTER_APP_GROUPS = YES;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
			};
			name = Debug;
		};
		8ED9FC8F2DFEC79A00727768 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = PimstatsDashboard/PimstatsDashboard.entitlements;
				CODE_SIGN_STYLE = Automatic;
				COMBINE_HIDPI_IMAGES = YES;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = PMN74SNV3Z;
				ENABLE_HARDENED_RUNTIME = YES;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/../Frameworks",
				);
				MACOSX_DEPLOYMENT_TARGET = 14.0;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = mihai.popa.dev.PimstatsDashboard;
				PRODUCT_NAME = "$(TARGET_NAME)";
				REGISTER_APP_GROUPS = YES;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
			};
			name = Release;
		};
		8ED9FC912DFEC79A00727768 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = PMN74SNV3Z;
				GENERATE_INFOPLIST_FILE = YES;
				MACOSX_DEPLOYMENT_TARGET = 14.0;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = mihai.popa.dev.PimstatsDashboardTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/PimstatsDashboard.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/PimstatsDashboard";
			};
			name = Debug;
		};
		8ED9FC922DFEC79A00727768 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = PMN74SNV3Z;
				GENERATE_INFOPLIST_FILE = YES;
				MACOSX_DEPLOYMENT_TARGET = 14.0;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = mihai.popa.dev.PimstatsDashboardTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/PimstatsDashboard.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/PimstatsDashboard";
			};
			name = Release;
		};
		8ED9FC942DFEC79A00727768 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = PMN74SNV3Z;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = mihai.popa.dev.PimstatsDashboardUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TEST_TARGET_NAME = PimstatsDashboard;
			};
			name = Debug;
		};
		8ED9FC952DFEC79A00727768 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = PMN74SNV3Z;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = mihai.popa.dev.PimstatsDashboardUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TEST_TARGET_NAME = PimstatsDashboard;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		8ED9FC642DFEC79800727768 /* Build configuration list for PBXProject "PimstatsDashboard" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				8ED9FC8B2DFEC79A00727768 /* Debug */,
				8ED9FC8C2DFEC79A00727768 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		8ED9FC8D2DFEC79A00727768 /* Build configuration list for PBXNativeTarget "PimstatsDashboard" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				8ED9FC8E2DFEC79A00727768 /* Debug */,
				8ED9FC8F2DFEC79A00727768 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		8ED9FC902DFEC79A00727768 /* Build configuration list for PBXNativeTarget "PimstatsDashboardTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				8ED9FC912DFEC79A00727768 /* Debug */,
				8ED9FC922DFEC79A00727768 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		8ED9FC932DFEC79A00727768 /* Build configuration list for PBXNativeTarget "PimstatsDashboardUITests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				8ED9FC942DFEC79A00727768 /* Debug */,
				8ED9FC952DFEC79A00727768 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 8ED9FC612DFEC79800727768 /* Project object */;
}
