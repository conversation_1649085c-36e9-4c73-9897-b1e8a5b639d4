# Deployment and Testing Guide

## Overview

This document provides comprehensive information about testing strategies, deployment procedures, and maintenance practices for the PimstatsDashboard application.

## Testing Strategy

### 1. Unit Testing

**Location**: `PimstatsDashboardTests/`

**Test Structure**:

```swift
import Testing
@testable import PimstatsDashboard

struct FootballAPIServiceTests {
    
    // MARK: - Model Tests
    
    @Test func testTeamDetailsDecoding() async throws {
        let teamData = """
        {
            "id": 57,
            "name": "Arsenal FC",
            "crest": "https://crests.football-data.org/57.png",
            "clubColors": "Red / White"
        }
        """.data(using: .utf8)!
        
        let team = try JSONDecoder().decode(TeamDetails.self, from: teamData)
        #expect(team.id == 57)
        #expect(team.name == "Arsenal FC")
        #expect(team.crest == "https://crests.football-data.org/57.png")
        #expect(team.clubColors == "Red / White")
    }
    
    @Test func testMatchDecoding() async throws {
        let matchData = """
        {
            "status": "FINISHED",
            "matchday": 1,
            "utcDate": "2024-08-16T19:00:00Z",
            "homeTeam": {"id": 57, "name": "Arsenal FC"},
            "awayTeam": {"id": 65, "name": "Manchester City FC"},
            "score": {
                "fullTime": {"home": 2, "away": 1}
            }
        }
        """.data(using: .utf8)!
        
        let match = try JSONDecoder().decode(Match.self, from: matchData)
        #expect(match.status == "FINISHED")
        #expect(match.matchday == 1)
        #expect(match.homeTeam.id == 57)
        #expect(match.score.fullTime.home == 2)
    }
    
    // MARK: - Service Tests
    
    @Test func testFirebaseTeamConversion() async throws {
        let apiTeam = TeamDetails(
            id: 57,
            name: "Arsenal FC",
            crest: "https://example.com/arsenal.png",
            clubColors: "Red / White"
        )
        
        let firebaseTeam = FirebaseTeam(from: apiTeam, league: "PL")
        
        #expect(firebaseTeam.teamId == 57)
        #expect(firebaseTeam.name == "Arsenal FC")
        #expect(firebaseTeam.league == "PL")
        #expect(firebaseTeam.documentPath == "teams/PL_57")
    }
    
    @Test func testMatchdayPerformanceCalculation() async throws {
        let performance = MatchdayPerformance(
            matchday: 1,
            points: 3,
            goalsFor: 2,
            goalsAgainst: 1
        )
        
        #expect(performance.goalDifference == 1)
        #expect(performance.matchday == 1)
        #expect(performance.points == 3)
    }
    
    // MARK: - Data Processing Tests
    
    @Test func testLeagueStatsCalculation() async throws {
        let teams = [
            TeamDetails(id: 1, name: "Team A", crest: nil, clubColors: nil),
            TeamDetails(id: 2, name: "Team B", crest: nil, clubColors: nil)
        ]
        
        let matches = [
            createMockMatch(homeTeamId: 1, awayTeamId: 2, homeGoals: 2, awayGoals: 1, matchday: 1)
        ]
        
        let apiService = FootballAPIService()
        let (stats, teamNames, matchdays, _, _) = apiService.processData(matches, teams)
        
        #expect(teamNames.count == 2)
        #expect(matchdays.contains(1))
        #expect(stats["Team A"]?[1]?.points == 3)
        #expect(stats["Team B"]?[1]?.points == 0)
    }
    
    // MARK: - Helper Methods
    
    private func createMockMatch(
        homeTeamId: Int,
        awayTeamId: Int,
        homeGoals: Int,
        awayGoals: Int,
        matchday: Int
    ) -> Match {
        return Match(
            status: "FINISHED",
            matchday: matchday,
            utcDate: "2024-08-16T19:00:00Z",
            homeTeam: Match.Team(id: homeTeamId, name: "Team \(homeTeamId)"),
            awayTeam: Match.Team(id: awayTeamId, name: "Team \(awayTeamId)"),
            score: Match.Score(
                fullTime: Match.Score.FullTime(home: homeGoals, away: awayGoals)
            )
        )
    }
}
```

### 2. Integration Testing

**Purpose**: Test interactions between services and external dependencies

```swift
struct IntegrationTests {
    
    @Test func testAPIToFirebaseSync() async throws {
        // This test requires a test Firebase project
        let apiService = FootballAPIService()
        let firebaseService = FirebaseService()
        let syncService = LeagueSeasonSyncService()
        
        // Test with a small dataset
        let result = await syncService.syncLeagueSeason(
            league: .premierLeague,
            season: 2023,
            forceRefresh: true
        )
        
        #expect(result.success == true)
        #expect(result.teamsCount > 0)
        #expect(result.matchdaysCount > 0)
    }
    
    @Test func testFirebaseDataRetrieval() async throws {
        let firebaseService = FirebaseService()
        
        // Test fetching teams
        let teams = try await firebaseService.fetchTeams(for: "PL")
        #expect(!teams.isEmpty)
        
        // Test fetching season performances
        let performances = try await firebaseService.fetchTeamSeasonPerformances(for: 2023, league: "PL")
        #expect(!performances.isEmpty)
    }
    
    @Test func testDuplicatePrevention() async throws {
        let syncService = LeagueSeasonSyncService()
        
        // First sync
        let firstResult = await syncService.syncLeagueSeason(
            league: .premierLeague,
            season: 2023,
            forceRefresh: false
        )
        
        // Second sync (should detect duplicate)
        let secondResult = await syncService.syncLeagueSeason(
            league: .premierLeague,
            season: 2023,
            forceRefresh: false
        )
        
        #expect(secondResult.wasAlreadySynced == true)
    }
}
```

### 3. UI Testing

**Location**: `PimstatsDashboardUITests/`

```swift
import XCTest

final class PimstatsDashboardUITests: XCTestCase {
    
    override func setUpWithError() throws {
        continueAfterFailure = false
    }
    
    func testMainNavigationFlow() throws {
        let app = XCUIApplication()
        app.launch()
        
        // Test main menu navigation
        XCTAssertTrue(app.staticTexts["Pimstats Dashboard"].exists)
        
        // Navigate to Quick Sync
        app.buttons["Quick Sync & Test"].tap()
        XCTAssertTrue(app.staticTexts["Quick Sync"].exists)
        
        // Navigate to Bar Race
        app.buttons["Bar Race Animation"].tap()
        XCTAssertTrue(app.staticTexts["Bar Race"].exists)
    }
    
    func testLeagueSeasonSync() throws {
        let app = XCUIApplication()
        app.launch()
        
        // Navigate to sync view
        app.buttons["League & Season Sync"].tap()
        
        // Select league
        app.buttons["League"].tap()
        app.buttons["Premier League"].tap()
        
        // Select season
        app.buttons["Season"].tap()
        app.buttons["2023/24"].tap()
        
        // Test sync button
        XCTAssertTrue(app.buttons["Sync Premier League 2023"].exists)
    }
    
    func testBarRaceAnimation() throws {
        let app = XCUIApplication()
        app.launch()
        
        // Navigate to bar race
        app.buttons["Bar Race Animation"].tap()
        
        // Test animation controls
        let playButton = app.buttons["Play"]
        XCTAssertTrue(playButton.exists)
        
        playButton.tap()
        
        // Verify pause button appears
        let pauseButton = app.buttons["Pause"]
        XCTAssertTrue(pauseButton.exists)
        
        // Test reset button
        let resetButton = app.buttons["Reset"]
        XCTAssertTrue(resetButton.exists)
        resetButton.tap()
    }
}
```

### 4. Performance Testing

```swift
struct PerformanceTests {
    
    @Test func testDataProcessingPerformance() async throws {
        let apiService = FootballAPIService()
        
        // Create large dataset
        let teams = (1...20).map { TeamDetails(id: $0, name: "Team \($0)", crest: nil, clubColors: nil) }
        let matches = createLargeMatchDataset(teams: teams, matchdays: 38)
        
        let startTime = CFAbsoluteTimeGetCurrent()
        
        let (stats, teamNames, matchdays, _, _) = apiService.processData(matches, teams)
        
        let timeElapsed = CFAbsoluteTimeGetCurrent() - startTime
        
        // Should process 380 matches in under 1 second
        #expect(timeElapsed < 1.0)
        #expect(teamNames.count == 20)
        #expect(matchdays.count == 38)
    }
    
    @Test func testFirebaseBatchPerformance() async throws {
        let firebaseService = FirebaseService()
        
        // Create test data
        let teams = (1...100).map { 
            FirebaseTeam(teamId: $0, name: "Team \($0)", league: "TEST")
        }
        
        let startTime = CFAbsoluteTimeGetCurrent()
        
        try await firebaseService.saveTeams(teams)
        
        let timeElapsed = CFAbsoluteTimeGetCurrent() - startTime
        
        // Should save 100 teams in under 5 seconds
        #expect(timeElapsed < 5.0)
    }
    
    private func createLargeMatchDataset(teams: [TeamDetails], matchdays: Int) -> [Match] {
        var matches: [Match] = []
        
        for matchday in 1...matchdays {
            for i in 0..<teams.count {
                for j in (i+1)..<teams.count {
                    let match = createMockMatch(
                        homeTeamId: teams[i].id,
                        awayTeamId: teams[j].id,
                        homeGoals: Int.random(in: 0...4),
                        awayGoals: Int.random(in: 0...4),
                        matchday: matchday
                    )
                    matches.append(match)
                }
            }
        }
        
        return matches
    }
}
```

## Build Configurations

### 1. Debug Configuration

**Purpose**: Development and testing

```swift
// Debug.xcconfig
FIREBASE_PROJECT_ID = pimstats-dev
API_BASE_URL = https://api.football-data.org/v4
LOG_LEVEL = DEBUG
ENABLE_MOCK_DATA = YES
CACHE_TIMEOUT = 60
```

**Swift Configuration**:

```swift
#if DEBUG
struct DebugConfig {
    static let firebaseProjectId = "pimstats-dev"
    static let apiBaseURL = "https://api.football-data.org/v4"
    static let logLevel = LogLevel.debug
    static let enableMockData = true
    static let cacheTimeout: TimeInterval = 60
    static let enablePerformanceMonitoring = true
}
#endif
```

### 2. Release Configuration

**Purpose**: Production deployment

```swift
// Release.xcconfig
FIREBASE_PROJECT_ID = pimstats-3b9a6
API_BASE_URL = https://api.football-data.org/v4
LOG_LEVEL = ERROR
ENABLE_MOCK_DATA = NO
CACHE_TIMEOUT = 300
```

**Swift Configuration**:

```swift
#if !DEBUG
struct ReleaseConfig {
    static let firebaseProjectId = "pimstats-3b9a6"
    static let apiBaseURL = "https://api.football-data.org/v4"
    static let logLevel = LogLevel.error
    static let enableMockData = false
    static let cacheTimeout: TimeInterval = 300
    static let enablePerformanceMonitoring = true
}
#endif
```

### 3. Test Configuration

**Purpose**: Automated testing

```swift
// Test.xcconfig
FIREBASE_PROJECT_ID = pimstats-test
API_BASE_URL = https://api.football-data.org/v4
LOG_LEVEL = NONE
ENABLE_MOCK_DATA = YES
CACHE_TIMEOUT = 0
```

## Deployment Process

### 1. Pre-Deployment Checklist

```bash
#!/bin/bash
# pre_deploy_check.sh

echo "🔍 Running pre-deployment checks..."

# 1. Run unit tests
echo "Running unit tests..."
xcodebuild test -project PimstatsDashboard.xcodeproj -scheme PimstatsDashboard -destination 'platform=iOS Simulator,name=iPhone 15'

if [ $? -ne 0 ]; then
    echo "❌ Unit tests failed"
    exit 1
fi

# 2. Run UI tests
echo "Running UI tests..."
xcodebuild test -project PimstatsDashboard.xcodeproj -scheme PimstatsDashboardUITests -destination 'platform=iOS Simulator,name=iPhone 15'

if [ $? -ne 0 ]; then
    echo "❌ UI tests failed"
    exit 1
fi

# 3. Check code coverage
echo "Checking code coverage..."
# Add code coverage checks here

# 4. Lint code
echo "Running SwiftLint..."
swiftlint

if [ $? -ne 0 ]; then
    echo "❌ SwiftLint failed"
    exit 1
fi

# 5. Check Firebase configuration
echo "Checking Firebase configuration..."
if [ ! -f "PimstatsDashboard/GoogleService-Info.plist" ]; then
    echo "❌ GoogleService-Info.plist not found"
    exit 1
fi

echo "✅ All pre-deployment checks passed"
```

### 2. Build and Archive

```bash
#!/bin/bash
# build_archive.sh

SCHEME="PimstatsDashboard"
CONFIGURATION="Release"
ARCHIVE_PATH="./build/PimstatsDashboard.xcarchive"

echo "🏗️ Building and archiving..."

# Clean build folder
rm -rf ./build
mkdir -p ./build

# Archive for iOS
xcodebuild archive \
    -project PimstatsDashboard.xcodeproj \
    -scheme $SCHEME \
    -configuration $CONFIGURATION \
    -archivePath $ARCHIVE_PATH \
    -destination 'generic/platform=iOS'

if [ $? -eq 0 ]; then
    echo "✅ Archive created successfully"
else
    echo "❌ Archive failed"
    exit 1
fi

# Export IPA
xcodebuild -exportArchive \
    -archivePath $ARCHIVE_PATH \
    -exportPath ./build \
    -exportOptionsPlist ExportOptions.plist

if [ $? -eq 0 ]; then
    echo "✅ IPA exported successfully"
else
    echo "❌ Export failed"
    exit 1
fi
```

### 3. Firebase Deployment

```bash
#!/bin/bash
# deploy_firebase.sh

echo "🔥 Deploying Firebase configuration..."

# 1. Deploy Firestore rules
firebase deploy --only firestore:rules --project pimstats-3b9a6

# 2. Deploy Firestore indexes
firebase deploy --only firestore:indexes --project pimstats-3b9a6

# 3. Deploy Cloud Functions (if any)
# firebase deploy --only functions --project pimstats-3b9a6

echo "✅ Firebase deployment complete"
```

## Monitoring and Analytics

### 1. Performance Monitoring

```swift
import FirebasePerformance

class PerformanceMonitor {
    
    static func startTrace(name: String) -> Trace? {
        #if !DEBUG
        return Performance.startTrace(name: name)
        #else
        return nil
        #endif
    }
    
    static func recordAPICall(endpoint: String, duration: TimeInterval, success: Bool) {
        let trace = startTrace(name: "api_call")
        trace?.setValue(endpoint, forAttribute: "endpoint")
        trace?.setValue(success ? "success" : "failure", forAttribute: "result")
        trace?.setMetric("duration_ms", value: Int64(duration * 1000))
        trace?.stop()
    }
    
    static func recordSyncOperation(league: String, season: Int, duration: TimeInterval, teamCount: Int) {
        let trace = startTrace(name: "sync_operation")
        trace?.setValue(league, forAttribute: "league")
        trace?.setValue(String(season), forAttribute: "season")
        trace?.setMetric("duration_ms", value: Int64(duration * 1000))
        trace?.setMetric("team_count", value: Int64(teamCount))
        trace?.stop()
    }
}
```

### 2. Crash Reporting

```swift
import FirebaseCrashlytics

class CrashReporter {
    
    static func recordError(_ error: Error, userInfo: [String: Any] = [:]) {
        #if !DEBUG
        Crashlytics.crashlytics().record(error: error, userInfo: userInfo)
        #endif
    }
    
    static func setUserID(_ userID: String) {
        #if !DEBUG
        Crashlytics.crashlytics().setUserID(userID)
        #endif
    }
    
    static func log(_ message: String) {
        #if !DEBUG
        Crashlytics.crashlytics().log(message)
        #endif
    }
}
```

### 3. Analytics

```swift
import FirebaseAnalytics

class AnalyticsManager {
    
    static func logEvent(_ name: String, parameters: [String: Any] = [:]) {
        #if !DEBUG
        Analytics.logEvent(name, parameters: parameters)
        #endif
    }
    
    static func logScreenView(_ screenName: String) {
        logEvent(AnalyticsEventScreenView, parameters: [
            AnalyticsParameterScreenName: screenName
        ])
    }
    
    static func logSyncEvent(league: String, season: Int, success: Bool) {
        logEvent("sync_data", parameters: [
            "league": league,
            "season": season,
            "success": success
        ])
    }
    
    static func logUserAction(_ action: String, parameters: [String: Any] = [:]) {
        logEvent("user_action", parameters: parameters.merging([
            "action": action
        ]) { _, new in new })
    }
}
```

## Maintenance Procedures

### 1. Regular Maintenance Tasks

```bash
#!/bin/bash
# maintenance.sh

echo "🔧 Running maintenance tasks..."

# 1. Update dependencies
echo "Updating dependencies..."
# Update Firebase SDK, other dependencies

# 2. Clean old data
echo "Cleaning old data..."
# Remove old cached data, logs, etc.

# 3. Check API status
echo "Checking API status..."
curl -s -o /dev/null -w "%{http_code}" https://api.football-data.org/v4/competitions/PL/teams

# 4. Verify Firebase connectivity
echo "Verifying Firebase connectivity..."
# Add Firebase connectivity check

# 5. Update documentation
echo "Checking documentation..."
# Verify documentation is up to date

echo "✅ Maintenance complete"
```

### 2. Monitoring Alerts

```yaml
# monitoring_config.yaml
alerts:
  - name: "High Error Rate"
    condition: "error_rate > 5%"
    notification: "email"
    
  - name: "API Response Time"
    condition: "api_response_time > 5s"
    notification: "slack"
    
  - name: "Firebase Quota"
    condition: "firebase_reads > 80% of quota"
    notification: "email"
    
  - name: "App Crash Rate"
    condition: "crash_rate > 1%"
    notification: "email, slack"
```

### 3. Backup Procedures

```bash
#!/bin/bash
# backup.sh

echo "💾 Creating backups..."

# 1. Export Firestore data
gcloud firestore export gs://pimstats-backups/$(date +%Y%m%d) --project=pimstats-3b9a6

# 2. Backup configuration files
tar -czf config_backup_$(date +%Y%m%d).tar.gz *.plist *.xcconfig

# 3. Backup documentation
tar -czf docs_backup_$(date +%Y%m%d).tar.gz Documentation/

echo "✅ Backup complete"
```
