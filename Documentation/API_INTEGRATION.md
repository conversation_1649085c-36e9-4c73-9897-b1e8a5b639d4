# Football Data API Integration Guide

## Overview

This document provides detailed information about the integration with the Football Data API (football-data.org), including endpoint usage, data processing, and error handling strategies.

## API Configuration

### Base Configuration

```swift
struct FootballAPIConfig {
    static let token = "0f296f6c02384ad6bb36d034affa7ada"
    static let baseURL = "https://api.football-data.org/v4"
    static let headers = ["X-Auth-Token": token]
}
```

### Supported Leagues

| League Code | Name | Country | Teams | Seasons |
|-------------|------|---------|-------|---------|
| PL | Premier League | England | 20 | 2015-2024 |
| PD | La Liga | Spain | 20 | 2015-2024 |
| BL1 | Bundesliga | Germany | 18 | 2015-2024 |
| SA | Serie A | Italy | 20 | 2015-2024 |
| FL1 | Ligue 1 | France | 20 | 2015-2024 |
| DED | Eredivisie | Netherlands | 18 | 2015-2024 |
| PPL | Primeira Liga | Portugal | 18 | 2015-2024 |
| ELC | Championship | England | 24 | 2015-2024 |

## API Endpoints

### 1. Teams Endpoint

**URL**: `GET /competitions/{leagueCode}/teams?season={year}`

**Purpose**: Fetch all teams participating in a specific league season

**Example Request**:
```http
GET https://api.football-data.org/v4/competitions/PL/teams?season=2024
X-Auth-Token: 0f296f6c02384ad6bb36d034affa7ada
```

**Response Structure**:
```json
{
  "count": 20,
  "filters": {
    "season": "2024"
  },
  "competition": {
    "id": 2021,
    "name": "Premier League",
    "code": "PL",
    "type": "LEAGUE",
    "emblem": "https://crests.football-data.org/PL.png"
  },
  "season": {
    "id": 1564,
    "startDate": "2024-08-16",
    "endDate": "2025-05-25",
    "currentMatchday": 15
  },
  "teams": [
    {
      "id": 57,
      "name": "Arsenal FC",
      "shortName": "Arsenal",
      "tla": "ARS",
      "crest": "https://crests.football-data.org/57.png",
      "address": "75 Drayton Park London N5 1BU",
      "website": "http://www.arsenal.com",
      "founded": 1886,
      "clubColors": "Red / White",
      "venue": "Emirates Stadium",
      "runningCompetitions": [...]
    }
  ]
}
```

### 2. Matches Endpoint

**URL**: `GET /competitions/{leagueCode}/matches?season={year}`

**Purpose**: Fetch all matches for a specific league season

**Example Request**:
```http
GET https://api.football-data.org/v4/competitions/PL/matches?season=2024
X-Auth-Token: 0f296f6c02384ad6bb36d034affa7ada
```

**Response Structure**:
```json
{
  "count": 380,
  "filters": {
    "season": "2024"
  },
  "competition": {
    "id": 2021,
    "name": "Premier League",
    "code": "PL"
  },
  "matches": [
    {
      "id": 456789,
      "utcDate": "2024-08-16T19:00:00Z",
      "status": "FINISHED",
      "matchday": 1,
      "stage": "REGULAR_SEASON",
      "group": null,
      "lastUpdated": "2024-08-16T21:00:00Z",
      "homeTeam": {
        "id": 57,
        "name": "Arsenal FC",
        "shortName": "Arsenal",
        "tla": "ARS",
        "crest": "https://crests.football-data.org/57.png"
      },
      "awayTeam": {
        "id": 65,
        "name": "Manchester City FC",
        "shortName": "Man City",
        "tla": "MCI",
        "crest": "https://crests.football-data.org/65.png"
      },
      "score": {
        "winner": "HOME_TEAM",
        "duration": "REGULAR",
        "fullTime": {
          "home": 2,
          "away": 1
        },
        "halfTime": {
          "home": 1,
          "away": 0
        }
      }
    }
  ]
}
```

## Data Processing Pipeline

### 1. Raw Data Extraction

```swift
/// Extracts team and match data from API responses
func extractRawData(teams: [TeamDetails], matches: [Match]) -> RawDataSet {
    let finishedMatches = matches.filter { $0.status == "FINISHED" }
    let sortedMatches = finishedMatches.sorted { 
        ($0.matchday, $0.utcDate) < ($1.matchday, $1.utcDate) 
    }
    
    return RawDataSet(
        teams: teams,
        matches: sortedMatches,
        matchdays: Set(sortedMatches.map { $0.matchday })
    )
}
```

### 2. League Table Calculation

```swift
/// Calculates cumulative league statistics for each matchday
func calculateLeagueTable(from matches: [Match], teams: [TeamDetails]) -> LeagueStats {
    var stats: LeagueStats = [:]
    var standings: [String: (points: Int, gf: Int, ga: Int)] = [:]
    
    // Initialize team lookup
    let teamIdToName = Dictionary(uniqueKeysWithValues: teams.map { ($0.id, $0.name) })
    
    for match in matches {
        guard let homeName = teamIdToName[match.homeTeam.id],
              let awayName = teamIdToName[match.awayTeam.id] else { continue }
        
        let homeGoals = match.score.fullTime.home ?? 0
        let awayGoals = match.score.fullTime.away ?? 0
        
        // Initialize if needed
        if standings[homeName] == nil { standings[homeName] = (0, 0, 0) }
        if standings[awayName] == nil { standings[awayName] = (0, 0, 0) }
        if stats[homeName] == nil { stats[homeName] = [:] }
        if stats[awayName] == nil { stats[awayName] = [:] }
        
        // Save current state before updating
        stats[homeName]![match.matchday] = standings[homeName]!
        stats[awayName]![match.matchday] = standings[awayName]!
        
        // Update standings based on result
        if homeGoals > awayGoals {
            standings[homeName]!.points += 3
        } else if homeGoals < awayGoals {
            standings[awayName]!.points += 3
        } else {
            standings[homeName]!.points += 1
            standings[awayName]!.points += 1
        }
        
        // Update goal statistics
        standings[homeName]!.gf += homeGoals
        standings[homeName]!.ga += awayGoals
        standings[awayName]!.gf += awayGoals
        standings[awayName]!.ga += homeGoals
    }
    
    return stats
}
```

### 3. Data Validation

```swift
/// Validates processed data for completeness and consistency
func validateProcessedData(stats: LeagueStats, teams: [String], matchdays: [Int]) throws {
    // Check minimum data requirements
    guard !teams.isEmpty else {
        throw DataValidationError.noTeamsFound
    }
    
    guard !matchdays.isEmpty else {
        throw DataValidationError.noMatchdaysFound
    }
    
    // Validate team count for league
    let expectedTeamCount = getExpectedTeamCount(for: league)
    guard teams.count == expectedTeamCount else {
        throw DataValidationError.incorrectTeamCount(expected: expectedTeamCount, actual: teams.count)
    }
    
    // Validate matchday progression
    let sortedMatchdays = matchdays.sorted()
    guard sortedMatchdays.first == 1 else {
        throw DataValidationError.invalidMatchdayStart
    }
    
    // Check for gaps in matchdays
    for i in 1..<sortedMatchdays.count {
        guard sortedMatchdays[i] == sortedMatchdays[i-1] + 1 else {
            throw DataValidationError.matchdayGap(missing: sortedMatchdays[i-1] + 1)
        }
    }
    
    // Validate points progression (non-decreasing)
    for (teamName, teamStats) in stats {
        let sortedStats = teamStats.sorted { $0.key < $1.key }
        for i in 1..<sortedStats.count {
            let prevPoints = sortedStats[i-1].value.points
            let currPoints = sortedStats[i].value.points
            guard currPoints >= prevPoints else {
                throw DataValidationError.invalidPointsProgression(team: teamName, matchday: sortedStats[i].key)
            }
        }
    }
}
```

## Error Handling

### API Error Types

```swift
enum FootballAPIError: Error, LocalizedError {
    case invalidURL
    case forbidden(season: Int)
    case rateLimitExceeded
    case badServerResponse(statusCode: Int)
    case decodingError(Error)
    case networkError(Error)
    
    var errorDescription: String? {
        switch self {
        case .invalidURL:
            return "Invalid API URL constructed"
        case .forbidden(let season):
            return "Season \(season) is not available or API limit reached"
        case .rateLimitExceeded:
            return "API rate limit exceeded. Please wait and try again."
        case .badServerResponse(let statusCode):
            return "Server error with status code: \(statusCode)"
        case .decodingError(let error):
            return "Failed to decode API response: \(error.localizedDescription)"
        case .networkError(let error):
            return "Network error: \(error.localizedDescription)"
        }
    }
    
    var recoverySuggestion: String? {
        switch self {
        case .forbidden:
            return "Try a different season or check your API subscription"
        case .rateLimitExceeded:
            return "Wait 60 seconds before making another request"
        case .badServerResponse:
            return "Check the API status page or try again later"
        case .decodingError:
            return "This may indicate an API format change"
        case .networkError:
            return "Check your internet connection"
        default:
            return nil
        }
    }
}
```

### Retry Logic

```swift
/// Implements exponential backoff retry logic for API requests
func performAPIRequest<T>(
    operation: @escaping () async throws -> T,
    maxRetries: Int = 3,
    baseDelay: TimeInterval = 1.0
) async throws -> T {
    var lastError: Error?
    
    for attempt in 0..<maxRetries {
        do {
            return try await operation()
        } catch let error as FootballAPIError {
            lastError = error
            
            switch error {
            case .rateLimitExceeded:
                // Wait longer for rate limit errors
                let delay = baseDelay * pow(2.0, Double(attempt)) * 10
                try await Task.sleep(nanoseconds: UInt64(delay * 1_000_000_000))
            case .networkError:
                // Retry network errors with exponential backoff
                let delay = baseDelay * pow(2.0, Double(attempt))
                try await Task.sleep(nanoseconds: UInt64(delay * 1_000_000_000))
            case .forbidden, .invalidURL, .decodingError:
                // Don't retry these errors
                throw error
            case .badServerResponse(let statusCode):
                if statusCode >= 500 {
                    // Retry server errors
                    let delay = baseDelay * pow(2.0, Double(attempt))
                    try await Task.sleep(nanoseconds: UInt64(delay * 1_000_000_000))
                } else {
                    // Don't retry client errors
                    throw error
                }
            }
        } catch {
            lastError = error
            // Don't retry unknown errors
            throw error
        }
    }
    
    throw lastError ?? FootballAPIError.networkError(NSError(domain: "RetryExhausted", code: -1))
}
```

## Rate Limiting

### API Limits

- **Free Tier**: 10 requests per minute
- **Paid Tier**: 100+ requests per minute
- **Daily Limits**: Vary by subscription

### Implementation Strategy

```swift
/// Rate limiter to prevent API quota exhaustion
actor APIRateLimiter {
    private var requestTimes: [Date] = []
    private let maxRequestsPerMinute: Int
    private let timeWindow: TimeInterval = 60.0
    
    init(maxRequestsPerMinute: Int = 10) {
        self.maxRequestsPerMinute = maxRequestsPerMinute
    }
    
    func waitIfNeeded() async {
        let now = Date()
        
        // Remove requests older than time window
        requestTimes = requestTimes.filter { now.timeIntervalSince($0) < timeWindow }
        
        if requestTimes.count >= maxRequestsPerMinute {
            // Calculate wait time until oldest request expires
            let oldestRequest = requestTimes.min() ?? now
            let waitTime = timeWindow - now.timeIntervalSince(oldestRequest)
            
            if waitTime > 0 {
                try? await Task.sleep(nanoseconds: UInt64(waitTime * 1_000_000_000))
            }
        }
        
        requestTimes.append(now)
    }
}
```

## Performance Optimization

### Caching Strategy

```swift
/// Caches API responses to reduce redundant requests
class APIResponseCache {
    private let cache = NSCache<NSString, CachedResponse>()
    private let cacheTimeout: TimeInterval = 300 // 5 minutes
    
    struct CachedResponse {
        let data: Data
        let timestamp: Date
    }
    
    func get(for key: String) -> Data? {
        guard let cached = cache.object(forKey: key as NSString) else { return nil }
        
        // Check if cache is still valid
        if Date().timeIntervalSince(cached.timestamp) > cacheTimeout {
            cache.removeObject(forKey: key as NSString)
            return nil
        }
        
        return cached.data
    }
    
    func set(_ data: Data, for key: String) {
        let cached = CachedResponse(data: data, timestamp: Date())
        cache.setObject(cached, forKey: key as NSString)
    }
}
```

### Batch Processing

```swift
/// Processes multiple API requests efficiently
func batchProcessLeagues(_ leagues: [League], season: Int) async throws -> [String: ProcessedData] {
    var results: [String: ProcessedData] = [:]
    
    // Process leagues in parallel with concurrency limit
    await withTaskGroup(of: (String, ProcessedData?).self, returning: Void.self) { group in
        let semaphore = AsyncSemaphore(value: 3) // Limit concurrent requests
        
        for league in leagues {
            group.addTask {
                await semaphore.wait()
                defer { semaphore.signal() }
                
                do {
                    let teams = try await fetchTeams(for: season, league: league)
                    let matches = try await fetchMatches(for: season, league: league)
                    let processed = processData(matches, teams)
                    return (league.id, processed)
                } catch {
                    print("Failed to process \(league.name): \(error)")
                    return (league.id, nil)
                }
            }
        }
        
        for await (leagueId, data) in group {
            if let data = data {
                results[leagueId] = data
            }
        }
    }
    
    return results
}
```
