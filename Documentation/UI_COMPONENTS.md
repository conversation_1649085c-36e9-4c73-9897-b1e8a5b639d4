# UI Components Documentation

## Overview

This document provides detailed information about the SwiftUI components, their architecture, data flow, and usage patterns in the PimstatsDashboard application.

## Architecture Patterns

### MVVM with Reactive Data Binding

```swift
// View Model Pattern
@MainActor
class ViewModelBase: ObservableObject {
    @Published var isLoading = false
    @Published var errorMessage: String?
    
    func handleError(_ error: Error) {
        errorMessage = error.localizedDescription
        isLoading = false
    }
    
    func startLoading() {
        isLoading = true
        errorMessage = nil
    }
}

// View Pattern
struct BaseView<ViewModel: ViewModelBase>: View {
    @StateObject var viewModel: ViewModel
    
    var body: some View {
        content
            .overlay(loadingOverlay)
            .alert("Error", isPresented: .constant(viewModel.errorMessage != nil)) {
                Button("OK") { viewModel.errorMessage = nil }
            } message: {
                Text(viewModel.errorMessage ?? "")
            }
    }
    
    @ViewBuilder
    var content: some View {
        // Implemented by subclasses
        EmptyView()
    }
    
    @ViewBuilder
    var loadingOverlay: some View {
        if viewModel.isLoading {
            ProgressView()
                .frame(maxWidth: .infinity, maxHeight: .infinity)
                .background(Color.black.opacity(0.3))
        }
    }
}
```

## Core UI Components

### 1. FootballDashboardView

**Purpose**: Main dashboard providing navigation and overview of football data

**Key Features**:
- Split view layout with sidebar navigation
- League and season selection
- Real-time data loading indicators
- Navigation to specialized views

**Component Structure**:

```swift
struct FootballDashboardView: View {
    // MARK: - State Management
    @StateObject private var dataSyncService = DataSyncService()
    @StateObject private var firebaseService = FirebaseService()
    @State private var selectedSeason: Int = 2024
    @State private var selectedLeague: League = League.premierLeague
    @State private var teams: [FirebaseTeam] = []
    @State private var seasonPerformances: [TeamSeasonPerformance] = []
    @State private var showingSyncView = false
    @State private var showingLeagueSeasonSync = false
    
    var body: some View {
        NavigationSplitView {
            sidebarContent
        } detail: {
            detailContent
        }
        .onAppear {
            Task { await loadInitialData() }
        }
        .onChange(of: selectedSeason) { _, newSeason in
            Task { await loadSeasonData(for: newSeason, league: selectedLeague.id) }
        }
        .onChange(of: selectedLeague) { _, newLeague in
            Task { await loadLeagueData(for: newLeague) }
        }
    }
    
    // MARK: - Sidebar Content
    @ViewBuilder
    private var sidebarContent: some View {
        VStack(spacing: 20) {
            headerSection
            Divider()
            leagueSelectionSection
            Divider()
            seasonSelectionSection
            Divider()
            navigationSection
            Spacer()
            syncControlsSection
        }
        .navigationSplitViewColumnWidth(min: 250, ideal: 300)
    }
    
    // MARK: - Header Section
    @ViewBuilder
    private var headerSection: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("Football Dashboard")
                .font(.title2)
                .fontWeight(.bold)
            
            Text("\(selectedLeague.name) Statistics")
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .padding(.horizontal)
    }
    
    // MARK: - League Selection
    @ViewBuilder
    private var leagueSelectionSection: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("League")
                .font(.headline)
                .padding(.horizontal)
            
            Picker("League", selection: $selectedLeague) {
                ForEach(League.supportedLeagues, id: \.id) { league in
                    Text(league.name).tag(league)
                }
            }
            .pickerStyle(.menu)
            .padding(.horizontal)
        }
    }
}
```

**Data Flow**:

```mermaid
graph TD
    A[User Selection] --> B[State Update]
    B --> C[Async Data Loading]
    C --> D[Firebase Service]
    D --> E[UI Update]
    E --> F[Published Properties]
    F --> G[View Refresh]
```

### 2. FootballBarRaceView

**Purpose**: Animated bar race chart showing league table evolution

**Key Features**:
- Timer-based animation system
- SwiftUI Charts integration
- Team logo display
- Interactive playback controls

**Animation Architecture**:

```swift
struct FootballBarRaceView: View {
    // MARK: - Animation State
    @StateObject private var dataManager = FirebaseDataManager()
    @State private var bars: [Entry] = []
    @State private var frameIndex = 0
    @State private var isAnimating = false
    
    // MARK: - Animation Timer
    private let ticker = Timer.publish(every: 2.0, on: .main, in: .common).autoconnect()
    
    var body: some View {
        VStack(spacing: 24) {
            controlsSection
            
            if !bars.isEmpty {
                animatedChart
                matchdayLabel
                playbackControls
            } else {
                emptyStateView
            }
        }
        .onReceive(ticker) { _ in
            if isAnimating {
                advanceFrame()
            }
        }
        .onAppear {
            Task { await dataManager.loadData() }
        }
        .onChange(of: dataManager.matchdays) { _, _ in
            updateBarsForCurrentFrame()
        }
    }
    
    // MARK: - Animated Chart
    @ViewBuilder
    private var animatedChart: some View {
        Chart {
            ForEach(bars.sorted { $0.value > $1.value }) { entry in
                BarMark(
                    x: .value("Points", entry.value),
                    y: .value("Team", entry.id),
                    height: .fixed(8)
                )
                .annotation(position: .trailing) {
                    HStack(spacing: 6) {
                        TeamLogo(teamName: entry.id, teamLogos: dataManager.teamLogos)
                        Text(Int(entry.value).formatted())
                            .font(.caption.bold())
                            .foregroundColor(.primary)
                    }
                }
                .annotation(position: .topLeading) {
                    Text(getShortTeamName(entry.id))
                        .font(.caption.bold())
                        .foregroundColor(.primary)
                }
                .foregroundStyle(getTeamColor(entry.id))
            }
        }
        .chartXAxis(.hidden)
        .chartYAxis(.hidden)
        .chartPlotStyle { plotArea in
            plotArea.background(.clear)
        }
        .frame(height: min(CGFloat(bars.count * 35 + 50), 500))
        .animation(.easeInOut(duration: 1.5), value: bars)
    }
    
    // MARK: - Animation Logic
    private func advanceFrame() {
        guard !dataManager.matchdays.isEmpty else { return }
        frameIndex = (frameIndex + 1) % dataManager.matchdays.count
        updateBarsForCurrentFrame()
    }
    
    private func updateBarsForCurrentFrame() {
        guard frameIndex < dataManager.matchdays.count else { return }
        let currentMatchday = dataManager.matchdays[frameIndex]
        bars = dataManager.getPointsForMatchday(currentMatchday)
    }
}
```

**Performance Considerations**:

```swift
// Optimized animation updates
extension FootballBarRaceView {
    
    /// Optimized frame updates with minimal state changes
    private func optimizedFrameUpdate() {
        // Only update if data has actually changed
        let newBars = dataManager.getPointsForMatchday(currentMatchday)
        
        if !barsAreEqual(bars, newBars) {
            withAnimation(.easeInOut(duration: 1.5)) {
                bars = newBars
            }
        }
    }
    
    /// Efficient comparison of bar data
    private func barsAreEqual(_ lhs: [Entry], _ rhs: [Entry]) -> Bool {
        guard lhs.count == rhs.count else { return false }
        
        for (left, right) in zip(lhs, rhs) {
            if left.id != right.id || left.value != right.value {
                return false
            }
        }
        
        return true
    }
}
```

### 3. LeagueSeasonSyncView

**Purpose**: Interface for synchronizing specific league/season combinations

**Key Features**:
- Visual league selection with cards
- Progress tracking during sync operations
- Duplicate prevention with user alerts
- Batch sync capabilities

**Component Architecture**:

```swift
struct LeagueSeasonSyncView: View {
    // MARK: - State Management
    @StateObject private var syncService = LeagueSeasonSyncService()
    @Environment(\.dismiss) private var dismiss
    
    @State private var selectedLeague: League = League.premierLeague
    @State private var selectedSeason: Int = 2024
    @State private var forceRefresh = false
    @State private var availableSeasons: [Int] = FootballAPIConfig.availableSeasons
    @State private var syncStatus: [String: Bool] = [:]
    @State private var showingResults = false
    
    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                headerSection
                leagueSelectionSection
                seasonSelectionSection
                syncStatusSection
                optionsSection
                progressSection
                Spacer()
                actionButtonsSection
            }
        }
        .onAppear {
            Task {
                await loadAvailableSeasons()
                await checkSyncStatus()
            }
        }
        .alert("Sync Result", isPresented: $showingResults) {
            Button("OK") {
                if syncService.lastSyncResult?.success == true {
                    dismiss()
                }
            }
        } message: {
            if let result = syncService.lastSyncResult {
                Text(result.displayMessage)
            }
        }
    }
    
    // MARK: - League Selection Section
    @ViewBuilder
    private var leagueSelectionSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("League")
                .font(.headline)
                .padding(.horizontal)
            
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 12) {
                    ForEach(League.supportedLeagues, id: \.id) { league in
                        LeagueCard(
                            league: league,
                            isSelected: selectedLeague.id == league.id,
                            onTap: {
                                selectedLeague = league
                                Task {
                                    await loadAvailableSeasons()
                                    await checkSyncStatus()
                                }
                            }
                        )
                    }
                }
                .padding(.horizontal)
            }
        }
    }
}
```

### 4. Team Logo Component

**Purpose**: Displays team logos with fallback styling

**Features**:
- Async image loading
- Fallback to styled circles
- Team color integration
- Caching support

```swift
struct TeamLogo: View {
    let teamName: String
    let teamLogos: [String: String]
    
    var body: some View {
        if let logoURL = teamLogos[teamName],
           let url = URL(string: logoURL) {
            AsyncImage(url: url) { image in
                image
                    .resizable()
                    .aspectRatio(contentMode: .fit)
            } placeholder: {
                TeamCircle(teamName: teamName)
            }
            .frame(width: 16, height: 16)
            .clipShape(Circle())
        } else {
            TeamCircle(teamName: teamName)
        }
    }
}

struct TeamCircle: View {
    let teamName: String
    
    var body: some View {
        let primaryColor = getTeamColor(teamName)
        let style = teamStyles[teamName] ?? TeamStyle(
            primaryColor: primaryColor,
            secondaryColor: nil,
            pattern: "solid"
        )
        
        getTeamFill(style: style)
            .frame(width: 12, height: 12)
            .overlay(
                Circle()
                    .stroke(Color.black.opacity(0.3), lineWidth: 0.5)
            )
    }
    
    @ViewBuilder
    private func getTeamFill(style: TeamStyle) -> some View {
        switch style.pattern {
        case "gradient":
            if let secondary = style.secondaryColor {
                Circle()
                    .fill(LinearGradient(
                        colors: [style.primaryColor, secondary],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    ))
            } else {
                Circle().fill(style.primaryColor)
            }
        case "stripe":
            if let secondary = style.secondaryColor {
                Circle()
                    .fill(RadialGradient(
                        colors: [style.primaryColor, secondary, style.primaryColor],
                        center: .center,
                        startRadius: 2,
                        endRadius: 6
                    ))
            } else {
                Circle().fill(style.primaryColor)
            }
        default: // "solid"
            Circle().fill(style.primaryColor)
        }
    }
}
```

## Reusable Components

### 1. Loading States

```swift
struct LoadingStateView: View {
    let message: String
    let showProgress: Bool
    let progress: Double?
    
    init(message: String = "Loading...", showProgress: Bool = false, progress: Double? = nil) {
        self.message = message
        self.showProgress = showProgress
        self.progress = progress
    }
    
    var body: some View {
        VStack(spacing: 16) {
            if showProgress, let progress = progress {
                ProgressView(value: progress)
                    .progressViewStyle(LinearProgressViewStyle())
                    .frame(width: 200)
            } else {
                ProgressView()
                    .scaleEffect(1.2)
            }
            
            Text(message)
                .font(.headline)
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(Color(.systemBackground))
    }
}
```

### 2. Error States

```swift
struct ErrorStateView: View {
    let title: String
    let message: String
    let retryAction: (() -> Void)?
    
    init(title: String = "Error", message: String, retryAction: (() -> Void)? = nil) {
        self.title = title
        self.message = message
        self.retryAction = retryAction
    }
    
    var body: some View {
        VStack(spacing: 20) {
            Image(systemName: "exclamationmark.triangle.fill")
                .font(.system(size: 60))
                .foregroundColor(.orange)
            
            VStack(spacing: 8) {
                Text(title)
                    .font(.title2)
                    .fontWeight(.bold)
                
                Text(message)
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
                    .padding(.horizontal)
            }
            
            if let retryAction = retryAction {
                Button("Retry") {
                    retryAction()
                }
                .buttonStyle(.borderedProminent)
            }
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(Color(.systemBackground))
    }
}
```

### 3. Empty States

```swift
struct EmptyStateView: View {
    let title: String
    let message: String
    let systemImage: String
    let actionTitle: String?
    let action: (() -> Void)?
    
    init(
        title: String,
        message: String,
        systemImage: String = "tray.fill",
        actionTitle: String? = nil,
        action: (() -> Void)? = nil
    ) {
        self.title = title
        self.message = message
        self.systemImage = systemImage
        self.actionTitle = actionTitle
        self.action = action
    }
    
    var body: some View {
        ContentUnavailableView(
            title,
            systemImage: systemImage,
            description: Text(message)
        ) {
            if let actionTitle = actionTitle, let action = action {
                Button(actionTitle) {
                    action()
                }
                .buttonStyle(.borderedProminent)
            }
        }
    }
}
```

## Navigation Patterns

### 1. Split View Navigation

```swift
struct SplitViewContainer<Sidebar: View, Detail: View>: View {
    let sidebar: Sidebar
    let detail: Detail
    
    init(@ViewBuilder sidebar: () -> Sidebar, @ViewBuilder detail: () -> Detail) {
        self.sidebar = sidebar()
        self.detail = detail()
    }
    
    var body: some View {
        NavigationSplitView {
            sidebar
                .navigationSplitViewColumnWidth(min: 250, ideal: 300, max: 400)
        } detail: {
            detail
        }
    }
}
```

### 2. Tab Navigation

```swift
struct TabContainer: View {
    @State private var selectedTab = 0
    
    var body: some View {
        TabView(selection: $selectedTab) {
            FootballDashboardView()
                .tabItem {
                    Label("Dashboard", systemImage: "chart.bar.fill")
                }
                .tag(0)
            
            FootballBarRaceView()
                .tabItem {
                    Label("Bar Race", systemImage: "chart.bar.xaxis")
                }
                .tag(1)
            
            SyncSummaryView()
                .tabItem {
                    Label("Sync", systemImage: "arrow.clockwise")
                }
                .tag(2)
        }
    }
}
```

## Accessibility

### 1. VoiceOver Support

```swift
extension FootballBarRaceView {
    
    private var accessibilityDescription: String {
        guard frameIndex < dataManager.matchdays.count else { return "" }
        
        let currentMatchday = dataManager.matchdays[frameIndex]
        let topTeams = bars.prefix(3)
        
        var description = "Matchday \(currentMatchday) standings. "
        
        for (index, entry) in topTeams.enumerated() {
            description += "\(index + 1). \(entry.id) with \(Int(entry.value)) points. "
        }
        
        return description
    }
    
    var accessibleChart: some View {
        animatedChart
            .accessibilityLabel("League table bar race chart")
            .accessibilityValue(accessibilityDescription)
            .accessibilityAddTraits(.updatesFrequently)
    }
}
```

### 2. Dynamic Type Support

```swift
extension View {
    func adaptiveFont(_ style: Font.TextStyle, maxSize: CGFloat? = nil) -> some View {
        self.font(.system(style, design: .default))
            .dynamicTypeSize(...(maxSize.map { DynamicTypeSize.accessibility1 } ?? .accessibility5))
    }
}

// Usage
Text("Team Name")
    .adaptiveFont(.headline, maxSize: 24)
```

### 3. Color Accessibility

```swift
extension Color {
    static func teamColor(for teamName: String, colorScheme: ColorScheme) -> Color {
        let baseColor = getTeamColor(teamName)
        
        // Adjust for accessibility in dark mode
        if colorScheme == .dark {
            return baseColor.opacity(0.8)
        } else {
            return baseColor
        }
    }
}
```

## Performance Optimization

### 1. View Optimization

```swift
struct OptimizedListView: View {
    let items: [Item]
    
    var body: some View {
        LazyVStack(spacing: 8) {
            ForEach(items, id: \.id) { item in
                ItemRowView(item: item)
                    .id(item.id) // Stable identity for animations
            }
        }
    }
}

struct ItemRowView: View {
    let item: Item
    
    var body: some View {
        // Use @ViewBuilder for conditional content
        content
    }
    
    @ViewBuilder
    private var content: some View {
        if item.isVisible {
            HStack {
                // Row content
            }
            .background(Color(.systemBackground))
        }
    }
}
```

### 2. Image Loading Optimization

```swift
struct OptimizedAsyncImage: View {
    let url: URL?
    let placeholder: AnyView
    
    @State private var imageData: Data?
    @State private var isLoading = false
    
    var body: some View {
        Group {
            if let imageData = imageData, let uiImage = UIImage(data: imageData) {
                Image(uiImage: uiImage)
                    .resizable()
                    .aspectRatio(contentMode: .fit)
            } else if isLoading {
                ProgressView()
                    .scaleEffect(0.5)
            } else {
                placeholder
            }
        }
        .onAppear {
            loadImage()
        }
    }
    
    private func loadImage() {
        guard let url = url, imageData == nil else { return }
        
        isLoading = true
        
        Task {
            do {
                let (data, _) = try await URLSession.shared.data(from: url)
                await MainActor.run {
                    self.imageData = data
                    self.isLoading = false
                }
            } catch {
                await MainActor.run {
                    self.isLoading = false
                }
            }
        }
    }
}
```
