# Firebase Integration Guide

## Overview

This document provides comprehensive information about Firebase Firestore integration, including schema design, security rules, performance optimization, and best practices.

## Firestore Database Schema

### Collection Structure

```
pimstats-3b9a6 (Database)
├── teams/
│   ├── PL_57 (Arsenal)
│   ├── PL_65 (Manchester City)
│   └── ...
├── team_season_performances/
│   ├── PL_2024_57 (Arsenal 2024)
│   ├── PL_2024_65 (Man City 2024)
│   └── ...
└── season_summaries/
    ├── PL_2024
    ├── PL_2023
    └── ...
```

### Document ID Patterns

| Collection | Pattern | Example | Purpose |
|------------|---------|---------|---------|
| teams | `{league}_{teamId}` | `PL_57` | Unique team per league |
| team_season_performances | `{league}_{season}_{teamId}` | `PL_2024_57` | Unique performance record |
| season_summaries | `{league}_{season}` | `PL_2024` | Season metadata |

## Data Models

### 1. Teams Collection

**Purpose**: Store unique team information across all leagues

**Document Structure**:
```json
{
  "teamId": 57,
  "name": "Arsenal FC",
  "crest": "https://crests.football-data.org/57.png",
  "clubColors": "Red / White",
  "league": "PL",
  "createdAt": "2024-06-15T10:00:00.000Z",
  "updatedAt": "2024-06-15T10:00:00.000Z"
}
```

**Swift Model**:
```swift
struct FirebaseTeam: Codable, Identifiable {
    @DocumentID var id: String?
    let teamId: Int            // Original API team ID
    let name: String           // Full team name
    let crest: String?         // Team logo URL
    let clubColors: String?    // Team colors description
    let league: String         // League code (PL, PD, etc.)
    let createdAt: Date        // Document creation timestamp
    let updatedAt: Date        // Last modification timestamp
    
    /// Computed property for Firestore document path
    var documentPath: String {
        return "teams/\(league)_\(teamId)"
    }
}
```

**Indexes Required**:
- `league` (ascending)
- `teamId` (ascending)
- `name` (ascending)

### 2. Team Season Performances Collection

**Purpose**: Store detailed performance data for each team per season

**Document Structure**:
```json
{
  "teamId": 57,
  "teamName": "Arsenal FC",
  "season": 2024,
  "league": "PL",
  "matchdayPerformances": [
    {
      "matchday": 1,
      "points": 3,
      "goalsFor": 2,
      "goalsAgainst": 1,
      "goalDifference": 1,
      "position": null,
      "updatedAt": "2024-08-16T21:00:00.000Z"
    },
    {
      "matchday": 2,
      "points": 6,
      "goalsFor": 4,
      "goalsAgainst": 1,
      "goalDifference": 3,
      "position": null,
      "updatedAt": "2024-08-23T21:00:00.000Z"
    }
  ],
  "totalPoints": 6,
  "totalGoalsFor": 4,
  "totalGoalsAgainst": 1,
  "totalGoalDifference": 3,
  "finalPosition": null,
  "createdAt": "2024-06-15T10:00:00.000Z",
  "updatedAt": "2024-08-23T21:00:00.000Z"
}
```

**Swift Models**:
```swift
struct MatchdayPerformance: Codable {
    let matchday: Int          // Matchday number (1-38 for PL)
    let points: Int            // Cumulative points at this matchday
    let goalsFor: Int          // Cumulative goals scored
    let goalsAgainst: Int      // Cumulative goals conceded
    let goalDifference: Int    // Calculated goal difference
    let position: Int?         // League position (optional)
    let updatedAt: Date        // When this matchday was last updated
}

struct TeamSeasonPerformance: Codable, Identifiable {
    @DocumentID var id: String?
    let teamId: Int                           // Team identifier
    let teamName: String                      // Team display name
    let season: Int                          // Season year
    let league: String                       // League code
    let matchdayPerformances: [MatchdayPerformance]  // All matchday data
    let totalPoints: Int                     // Final/current points
    let totalGoalsFor: Int                   // Total goals scored
    let totalGoalsAgainst: Int               // Total goals conceded
    let totalGoalDifference: Int             // Final goal difference
    let finalPosition: Int?                  // Final league position
    let createdAt: Date                      // Document creation
    let updatedAt: Date                      // Last modification
    
    var documentPath: String {
        return "team_season_performances/\(league)_\(season)_\(teamId)"
    }
}
```

**Indexes Required**:
- `season` (ascending), `league` (ascending)
- `teamId` (ascending), `season` (ascending)
- `league` (ascending), `totalPoints` (descending)

### 3. Season Summaries Collection

**Purpose**: Store metadata about each season for quick queries

**Document Structure**:
```json
{
  "season": 2024,
  "league": "PL",
  "totalMatchdays": 15,
  "totalTeams": 20,
  "isComplete": false,
  "lastUpdated": "2024-12-15T10:00:00.000Z",
  "createdAt": "2024-08-16T10:00:00.000Z"
}
```

**Swift Model**:
```swift
struct SeasonSummary: Codable, Identifiable {
    @DocumentID var id: String?
    let season: Int            // Season year
    let league: String         // League code
    let totalMatchdays: Int    // Number of matchdays played
    let totalTeams: Int        // Number of teams in league
    let isComplete: Bool       // Whether season is finished
    let lastUpdated: Date      // Last data update
    let createdAt: Date        // Document creation
    
    var documentPath: String {
        return "season_summaries/\(league)_\(season)"
    }
}
```

## Firebase Service Implementation

### Core Service Class

```swift
@MainActor
class FirebaseService: ObservableObject {
    private let db = Firestore.firestore()
    @Published var isLoading = false
    @Published var errorMessage: String?
    
    // MARK: - Team Operations
    
    /// Saves a single team with merge strategy
    func saveTeam(_ team: FirebaseTeam) async throws {
        let documentPath = team.documentPath
        
        do {
            try await db.document(documentPath).setData(from: team, merge: true)
            print("✅ Team saved: \(team.name)")
        } catch {
            print("❌ Error saving team \(team.name): \(error)")
            throw FirebaseError.saveFailed(entity: "team", error: error)
        }
    }
    
    /// Batch save operation for multiple teams
    func saveTeams(_ teams: [FirebaseTeam]) async throws {
        guard !teams.isEmpty else { return }
        
        let batch = db.batch()
        
        for team in teams {
            let documentRef = db.document(team.documentPath)
            do {
                try batch.setData(from: team, forDocument: documentRef, merge: true)
            } catch {
                throw FirebaseError.batchPreparationFailed(error: error)
            }
        }
        
        do {
            try await batch.commit()
            print("✅ Batch saved \(teams.count) teams")
        } catch {
            print("❌ Error committing team batch: \(error)")
            throw FirebaseError.batchCommitFailed(error: error)
        }
    }
    
    /// Fetches teams with optional filtering
    func fetchTeams(for league: String? = nil) async throws -> [FirebaseTeam] {
        do {
            var query: Query = db.collection("teams")
            
            if let league = league {
                query = query.whereField("league", isEqualTo: league)
            }
            
            let snapshot = try await query.getDocuments()
            
            let teams = try snapshot.documents.compactMap { document in
                try document.data(as: FirebaseTeam.self)
            }
            
            print("✅ Fetched \(teams.count) teams")
            return teams
        } catch {
            print("❌ Error fetching teams: \(error)")
            throw FirebaseError.fetchFailed(entity: "teams", error: error)
        }
    }
}
```

### Error Handling

```swift
enum FirebaseError: Error, LocalizedError {
    case saveFailed(entity: String, error: Error)
    case fetchFailed(entity: String, error: Error)
    case batchPreparationFailed(error: Error)
    case batchCommitFailed(error: Error)
    case documentNotFound(path: String)
    case invalidData(reason: String)
    case permissionDenied
    case quotaExceeded
    
    var errorDescription: String? {
        switch self {
        case .saveFailed(let entity, let error):
            return "Failed to save \(entity): \(error.localizedDescription)"
        case .fetchFailed(let entity, let error):
            return "Failed to fetch \(entity): \(error.localizedDescription)"
        case .batchPreparationFailed(let error):
            return "Failed to prepare batch operation: \(error.localizedDescription)"
        case .batchCommitFailed(let error):
            return "Failed to commit batch operation: \(error.localizedDescription)"
        case .documentNotFound(let path):
            return "Document not found at path: \(path)"
        case .invalidData(let reason):
            return "Invalid data: \(reason)"
        case .permissionDenied:
            return "Permission denied. Check Firestore security rules."
        case .quotaExceeded:
            return "Firestore quota exceeded. Try again later."
        }
    }
}
```

## Security Rules

### Development Rules (Permissive)

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Allow all reads and writes during development
    match /{document=**} {
      allow read, write: if true;
    }
  }
}
```

### Production Rules (Restrictive)

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Teams collection - read-only for clients
    match /teams/{teamId} {
      allow read: if true;
      allow write: if false; // Only server-side writes
    }
    
    // Team season performances - read-only for clients
    match /team_season_performances/{performanceId} {
      allow read: if true;
      allow write: if false; // Only server-side writes
    }
    
    // Season summaries - read-only for clients
    match /season_summaries/{summaryId} {
      allow read: if true;
      allow write: if false; // Only server-side writes
    }
    
    // Future: User-specific data
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
  }
}
```

### Advanced Rules with Validation

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Helper functions
    function isValidLeague(league) {
      return league in ['PL', 'PD', 'BL1', 'SA', 'FL1', 'DED', 'PPL', 'ELC'];
    }
    
    function isValidSeason(season) {
      return season >= 2015 && season <= 2030;
    }
    
    // Teams collection with validation
    match /teams/{teamId} {
      allow read: if true;
      allow create: if isValidTeamData(request.resource.data);
      allow update: if isValidTeamData(request.resource.data) 
                    && resource.data.teamId == request.resource.data.teamId;
      
      function isValidTeamData(data) {
        return data.keys().hasAll(['teamId', 'name', 'league', 'createdAt', 'updatedAt'])
               && isValidLeague(data.league)
               && data.teamId is int
               && data.name is string
               && data.createdAt is timestamp
               && data.updatedAt is timestamp;
      }
    }
    
    // Season performances with validation
    match /team_season_performances/{performanceId} {
      allow read: if true;
      allow create: if isValidPerformanceData(request.resource.data);
      allow update: if isValidPerformanceData(request.resource.data);
      
      function isValidPerformanceData(data) {
        return data.keys().hasAll(['teamId', 'season', 'league', 'matchdayPerformances'])
               && isValidLeague(data.league)
               && isValidSeason(data.season)
               && data.teamId is int
               && data.matchdayPerformances is list;
      }
    }
  }
}
```

## Performance Optimization

### Query Optimization

```swift
/// Optimized queries with proper indexing
class OptimizedFirebaseService {
    
    /// Fetch teams with pagination
    func fetchTeamsPaginated(
        league: String,
        limit: Int = 20,
        startAfter: DocumentSnapshot? = nil
    ) async throws -> (teams: [FirebaseTeam], lastDocument: DocumentSnapshot?) {
        
        var query = db.collection("teams")
            .whereField("league", isEqualTo: league)
            .order(by: "name")
            .limit(to: limit)
        
        if let startAfter = startAfter {
            query = query.start(afterDocument: startAfter)
        }
        
        let snapshot = try await query.getDocuments()
        let teams = try snapshot.documents.compactMap { try $0.data(as: FirebaseTeam.self) }
        
        return (teams: teams, lastDocument: snapshot.documents.last)
    }
    
    /// Fetch season data with field selection
    func fetchSeasonSummary(league: String, season: Int) async throws -> SeasonSummary? {
        let documentPath = "season_summaries/\(league)_\(season)"
        
        let document = try await db.document(documentPath)
            .getDocument(source: .cache) // Try cache first
        
        if document.exists {
            return try document.data(as: SeasonSummary.self)
        }
        
        // Fallback to server if not in cache
        let serverDocument = try await db.document(documentPath)
            .getDocument(source: .server)
        
        return try serverDocument.data(as: SeasonSummary.self)
    }
}
```

### Batch Operations

```swift
/// Efficient batch operations for large datasets
extension FirebaseService {
    
    /// Batch save with chunking for large datasets
    func saveLargeDataset<T: Codable>(_ items: [T], collection: String, getDocumentPath: (T) -> String) async throws {
        let chunkSize = 500 // Firestore batch limit
        let chunks = items.chunked(into: chunkSize)
        
        for (index, chunk) in chunks.enumerated() {
            print("Processing chunk \(index + 1) of \(chunks.count)")
            
            let batch = db.batch()
            
            for item in chunk {
                let documentPath = getDocumentPath(item)
                let documentRef = db.document(documentPath)
                try batch.setData(from: item, forDocument: documentRef, merge: true)
            }
            
            try await batch.commit()
            
            // Add delay between batches to avoid rate limiting
            if index < chunks.count - 1 {
                try await Task.sleep(nanoseconds: 100_000_000) // 0.1 seconds
            }
        }
    }
}

extension Array {
    func chunked(into size: Int) -> [[Element]] {
        return stride(from: 0, to: count, by: size).map {
            Array(self[$0..<Swift.min($0 + size, count)])
        }
    }
}
```

### Caching Strategy

```swift
/// Local caching to reduce Firestore reads
class CachedFirebaseService: FirebaseService {
    private let cache = NSCache<NSString, CachedData>()
    private let cacheTimeout: TimeInterval = 300 // 5 minutes
    
    struct CachedData {
        let data: Any
        let timestamp: Date
    }
    
    override func fetchTeams(for league: String?) async throws -> [FirebaseTeam] {
        let cacheKey = "teams_\(league ?? "all")"
        
        // Check cache first
        if let cached = getCachedData(for: cacheKey) as? [FirebaseTeam] {
            return cached
        }
        
        // Fetch from Firestore
        let teams = try await super.fetchTeams(for: league)
        
        // Cache the result
        setCachedData(teams, for: cacheKey)
        
        return teams
    }
    
    private func getCachedData(for key: String) -> Any? {
        guard let cached = cache.object(forKey: key as NSString) else { return nil }
        
        if Date().timeIntervalSince(cached.timestamp) > cacheTimeout {
            cache.removeObject(forKey: key as NSString)
            return nil
        }
        
        return cached.data
    }
    
    private func setCachedData(_ data: Any, for key: String) {
        let cached = CachedData(data: data, timestamp: Date())
        cache.setObject(cached, forKey: key as NSString)
    }
}
```

## Monitoring and Analytics

### Performance Monitoring

```swift
/// Firebase Performance monitoring integration
import FirebasePerformance

extension FirebaseService {
    
    func monitoredFetchTeams(for league: String) async throws -> [FirebaseTeam] {
        let trace = Performance.startTrace(name: "fetch_teams")
        trace?.setValue(league, forAttribute: "league")
        
        defer {
            trace?.stop()
        }
        
        do {
            let teams = try await fetchTeams(for: league)
            trace?.setValue(String(teams.count), forAttribute: "team_count")
            trace?.setValue("success", forAttribute: "result")
            return teams
        } catch {
            trace?.setValue("error", forAttribute: "result")
            trace?.setValue(error.localizedDescription, forAttribute: "error_message")
            throw error
        }
    }
}
```

### Custom Metrics

```swift
/// Custom metrics for business logic monitoring
class FirebaseMetrics {
    
    static func recordSyncOperation(
        league: String,
        season: Int,
        duration: TimeInterval,
        success: Bool,
        teamCount: Int
    ) {
        let trace = Performance.startTrace(name: "sync_operation")
        trace?.setValue(league, forAttribute: "league")
        trace?.setValue(String(season), forAttribute: "season")
        trace?.setValue(success ? "success" : "failure", forAttribute: "result")
        trace?.setValue(String(teamCount), forAttribute: "team_count")
        
        // Record duration as a custom metric
        trace?.setMetric("duration_ms", value: Int64(duration * 1000))
        
        trace?.stop()
    }
    
    static func recordUserAction(_ action: String, parameters: [String: String] = [:]) {
        Analytics.logEvent(action, parameters: parameters)
    }
}
```
