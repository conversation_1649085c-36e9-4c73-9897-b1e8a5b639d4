//
//  MainMenuView.swift
//  PimstatsDashboard
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 15.06.2025.
//

import SwiftUI

struct MainMenuView: View {
    var body: some View {
        NavigationView {
            List {
                Section("Data Management") {
                    NavigationLink(destination: QuickSyncView()) {
                        Label("Quick Sync & Test", systemImage: "arrow.clockwise")
                    }
                    
                    NavigationLink(destination: LeagueSeasonSyncView()) {
                        Label("League & Season Sync", systemImage: "plus.circle")
                    }
                    
                    NavigationLink(destination: SyncSummaryView()) {
                        Label("Sync Summary", systemImage: "list.bullet.clipboard")
                    }
                }
                
                Section("Dashboard") {
                    NavigationLink(destination: FootballDashboardView()) {
                        Label("Football Dashboard", systemImage: "chart.bar.fill")
                    }

                    NavigationLink(destination: FootballBarRaceView()) {
                        Label("Bar Race Animation", systemImage: "chart.bar.xaxis")
                    }

                    NavigationLink(destination: SystemTestView()) {
                        Label("System Test", systemImage: "checkmark.shield")
                    }
                }
                
                Section("Setup") {
                    NavigationLink(destination: FirebaseSetupView()) {
                        Label("Firebase Setup", systemImage: "gear")
                    }
                    
                    NavigationLink(destination: WelcomeView()) {
                        Label("Welcome Screen", systemImage: "house")
                    }
                }
            }
            .navigationTitle("Pimstats Dashboard")
        }
    }
}

#Preview {
    MainMenuView()
}
