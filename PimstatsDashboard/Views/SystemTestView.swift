//
//  SystemTestView.swift
//  PimstatsDashboard
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 15.06.2025.
//

import SwiftUI

struct SystemTestView: View {
    @StateObject private var coordinator = FootballDataCoordinator()
    @State private var testResults: [String: Any] = [:]
    @State private var isRunningTest = false
    @State private var showingResults = false
    
    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                // Header
                VStack(spacing: 8) {
                    Image(systemName: "checkmark.shield.fill")
                        .font(.system(size: 60))
                        .foregroundColor(.blue)
                    
                    Text("System Test")
                        .font(.title)
                        .fontWeight(.bold)
                    
                    Text("Test API and Firebase connectivity")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                }
                .padding()
                
                // Status Cards
                LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 16) {
                    StatusCard(
                        title: "Initialization",
                        status: coordinator.isInitialized ? .success : .pending,
                        value: coordinator.isInitialized ? "Ready" : "Pending"
                    )
                    
                    StatusCard(
                        title: "Available Seasons",
                        status: coordinator.availableSeasons.isEmpty ? .pending : .success,
                        value: "\(coordinator.availableSeasons.count)"
                    )
                    
                    StatusCard(
                        title: "Synced Seasons",
                        status: coordinator.syncedSeasons.isEmpty ? .warning : .success,
                        value: "\(coordinator.syncedSeasons.count)"
                    )
                    
                    StatusCard(
                        title: "Current Season",
                        status: .info,
                        value: "\(coordinator.currentSeason)"
                    )
                }
                .padding(.horizontal)
                
                // Error Message
                if let errorMessage = coordinator.errorMessage {
                    Text(errorMessage)
                        .font(.caption)
                        .foregroundColor(.red)
                        .multilineTextAlignment(.center)
                        .padding()
                        .background(Color.red.opacity(0.1))
                        .cornerRadius(8)
                        .padding(.horizontal)
                }
                
                Spacer()
                
                // Action Buttons
                VStack(spacing: 12) {
                    Button(action: {
                        Task {
                            await coordinator.initialize()
                        }
                    }) {
                        Label("Initialize System", systemImage: "power")
                            .frame(maxWidth: .infinity)
                    }
                    .buttonStyle(.borderedProminent)
                    .disabled(coordinator.isInitialized)
                    
                    Button(action: {
                        Task {
                            isRunningTest = true
                            let success = await coordinator.runSystemTest()
                            testResults = await coordinator.getSystemStatus()
                            isRunningTest = false
                            showingResults = true
                        }
                    }) {
                        Label("Run System Test", systemImage: "play.circle")
                            .frame(maxWidth: .infinity)
                    }
                    .buttonStyle(.bordered)
                    .disabled(isRunningTest || !coordinator.isInitialized)
                    
                    if !coordinator.syncedSeasons.isEmpty {
                        NavigationLink(destination: FootballDashboardView()) {
                            Label("Open Dashboard", systemImage: "chart.bar.fill")
                                .frame(maxWidth: .infinity)
                        }
                        .buttonStyle(.borderedProminent)
                    }
                }
                .padding(.horizontal)
                
                if isRunningTest {
                    ProgressView("Running system test...")
                        .padding()
                }
            }
            .navigationTitle("System Test")
            .navigationBarTitleDisplayMode(.inline)
            .onAppear {
                Task {
                    await coordinator.initialize()
                }
            }
            .sheet(isPresented: $showingResults) {
                TestResultsView(results: testResults)
            }
        }
    }
}

struct StatusCard: View {
    let title: String
    let status: StatusType
    let value: String
    
    enum StatusType {
        case success, warning, error, pending, info
        
        var color: Color {
            switch self {
            case .success: return .green
            case .warning: return .orange
            case .error: return .red
            case .pending: return .gray
            case .info: return .blue
            }
        }
        
        var icon: String {
            switch self {
            case .success: return "checkmark.circle.fill"
            case .warning: return "exclamationmark.triangle.fill"
            case .error: return "xmark.circle.fill"
            case .pending: return "clock.fill"
            case .info: return "info.circle.fill"
            }
        }
    }
    
    var body: some View {
        VStack(spacing: 8) {
            Image(systemName: status.icon)
                .font(.title2)
                .foregroundColor(status.color)
            
            Text(value)
                .font(.title2)
                .fontWeight(.bold)
            
            Text(title)
                .font(.caption)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
        }
        .frame(maxWidth: .infinity)
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
}

struct TestResultsView: View {
    let results: [String: Any]
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            List {
                ForEach(results.keys.sorted(), id: \.self) { key in
                    HStack {
                        Text(key.capitalized)
                            .font(.headline)
                        
                        Spacer()
                        
                        Text("\(results[key] ?? "N/A")")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                    }
                }
            }
            .navigationTitle("Test Results")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
        }
    }
}

#Preview {
    SystemTestView()
}
