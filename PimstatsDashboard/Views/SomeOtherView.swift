struct SomeOtherView: View {
    @EnvironmentObject var coordinator: FootballDataCoordinator
    
    var body: some View {
        VStack {
            Text("Current Data Source: \(coordinator.dataSource == .footballDataAPI ? "Football-data.org API" : "GitHub Repository")")
            
            Button("Switch Data Source") {
                let newSource = coordinator.dataSource == .footballDataAPI ? 
                    FootballDataSource.githubRepository : FootballDataSource.footballDataAPI
                
                Task {
                    await coordinator.switchDataSource(to: newSource)
                }
            }
        }
    }
}