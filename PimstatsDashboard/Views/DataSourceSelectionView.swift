//
//  DataSourceSelectionView.swift
//  PimstatsDashboard
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 15.06.2025.
//

import SwiftUI

struct DataSourceSelectionView: View {
    @ObservedObject var coordinator: FootballDataCoordinator
    @State private var isLoading = false
    @State private var testResult = ""
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Data Source Selection")
                .font(.headline)
            
            Picker("Data Source", selection: $coordinator.dataSource) {
                Text("Football-data.org API").tag(FootballDataSource.footballDataAPI)
                Text("GitHub Repository").tag(FootballDataSource.githubRepository)
            }
            .pickerStyle(SegmentedPickerStyle())
            .onChange(of: coordinator.dataSource) { _, newValue in
                Task {
                    isLoading = true
                    await coordinator.switchDataSource(to: newValue)
                    isLoading = false
                }
            }
            
            Divider()
            
            <PERSON><PERSON>("Test Connection") {
                testDataSource()
            }
            .disabled(isLoading)
            
            if !testResult.isEmpty {
                ScrollView {
                    Text(testResult)
                        .font(.system(.body, design: .monospaced))
                        .padding()
                        .frame(maxWidth: .infinity, alignment: .leading)
                        .background(Color(.gray))
                        .cornerRadius(8)
                }
                .frame(height: 200)
            }
            
            if isLoading {
                ProgressView()
                    .progressViewStyle(CircularProgressViewStyle())
            }
        }
        .padding()
    }
    
    private func testDataSource() {
        testResult = ""
        isLoading = true
        
        Task {
            testResult = "Testing connection to \(coordinator.dataSource == .footballDataAPI ? "Football-data.org API" : "GitHub Repository")...\n"
            
            let success = await coordinator.testAPIConnectivity()
            
            if success {
                testResult += "✅ Connection successful!\n"
                testResult += "Available seasons: \(coordinator.availableSeasons.map {"\($0)" }.joined(separator: ", "))\n"
            } else {
                testResult += "❌ Connection failed: \(coordinator.errorMessage ?? "Unknown error")\n"
            }
            
            isLoading = false
        }
    }
}
