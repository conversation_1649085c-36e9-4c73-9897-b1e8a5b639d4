//
//  TeamsListView.swift
//  PimstatsDashboard
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 15.06.2025.
//

import SwiftUI

struct TeamsListView: View {
    let teams: [FirebaseTeam]
    @State private var searchText = ""
    
    var filteredTeams: [FirebaseTeam] {
        if searchText.isEmpty {
            return teams.sorted { $0.name < $1.name }
        } else {
            return teams.filter { $0.name.localizedCaseInsensitiveContains(searchText) }
                .sorted { $0.name < $1.name }
        }
    }
    
    var body: some View {
        List {
            ForEach(filteredTeams, id: \.teamId) { team in
                TeamRowView(team: team)
            }
        }
        .searchable(text: $searchText, prompt: "Search teams...")
        .navigationTitle("Teams")
    }
}

struct TeamRowView: View {
    let team: FirebaseTeam
    
    var body: some View {
        HStack(spacing: 12) {
            // Team Logo (placeholder for now)
            AsyncImage(url: URL(string: team.crest ?? "")) { image in
                image
                    .resizable()
                    .aspectRatio(contentMode: .fit)
            } placeholder: {
                Image(systemName: "shield.fill")
                    .foregroundColor(.blue)
            }
            .frame(width: 40, height: 40)
            .background(Color(.systemGray))
            .cornerRadius(8)
            
            VStack(alignment: .leading, spacing: 4) {
                Text(team.name)
                    .font(.headline)
                    .lineLimit(1)
                
                if let colors = team.clubColors {
                    Text(colors)
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .lineLimit(1)
                }
                
                Text("ID: \(team.teamId)")
                    .font(.caption2)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
            
            VStack(alignment: .trailing, spacing: 4) {
                Text(team.league)
                    .font(.caption)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 2)
                    .background(Color.blue.opacity(0.1))
                    .foregroundColor(.blue)
                    .cornerRadius(4)
                
                Text("Updated: \(team.updatedAt, style: .date)")
                    .font(.caption2)
                    .foregroundColor(.secondary)
            }
        }
        .padding(.vertical, 4)
    }
}

#Preview {
    NavigationView {
        TeamsListView(teams: [
            FirebaseTeam(teamId: 1, name: "Arsenal", crest: nil, clubColors: "Red / White"),
            FirebaseTeam(teamId: 2, name: "Manchester City", crest: nil, clubColors: "Sky Blue / White"),
            FirebaseTeam(teamId: 3, name: "Liverpool", crest: nil, clubColors: "Red")
        ])
    }
}
