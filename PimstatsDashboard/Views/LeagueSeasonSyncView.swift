//
//  LeagueSeasonSyncView.swift
//  PimstatsDashboard
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 15.06.2025.
//

import SwiftUI

struct LeagueSeasonSyncView: View {
    @StateObject private var syncService = LeagueSeasonSyncService()
    @Environment(\.dismiss) private var dismiss
    
    @State private var selectedLeague: League = League.premierLeague
    @State private var selectedSeason: Int = 2024
    @State private var forceRefresh = false
    @State private var availableSeasons: [Int] = FootballAPIConfig.availableSeasons
    @State private var syncStatus: [String: Bool] = [:]
    @State private var showingResults = false
    @State private var syncResults: [LeagueSeasonSyncService.SyncResult] = []
    
    var currentSyncKey: String {
        return "\(selectedLeague.id)_\(selectedSeason)"
    }
    
    var isAlreadySynced: Bool {
        return syncStatus[currentSyncKey] == true
    }
    
    var leagueSelectionView: some View {
        // League Selection
        VStack(alignment: .leading, spacing: 12) {
            Text("League")
                .font(.headline)
                .padding(.horizontal)
            
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 12) {
                    ForEach(League.supportedLeagues, id: \.id) { league in
                        LeagueCard(
                            league: league,
                            isSelected: selectedLeague.id == league.id,
                            onTap: {
                                selectedLeague = league
                                Task {
                                    await loadAvailableSeasons()
                                    await checkSyncStatus()
                                }
                            }
                        )
                    }
                }
                .padding(.horizontal)
            }
        }
    }
    
    var seasonSelectionView: some View {
        // Season Selection
        VStack(alignment: .leading, spacing: 12) {
            Text("Season")
                .font(.headline)
                .padding(.horizontal)
            
            Picker("Season", selection: $selectedSeason) {
                ForEach(availableSeasons, id: \.self) { season in
                    HStack {
                        Text("\(season)/\(String(season + 1).suffix(2))")
                        Spacer()
                        if syncStatus["\(selectedLeague.id)_\(season)"] == true {
                            Image(systemName: "checkmark.circle.fill")
                                .foregroundColor(.green)
                                .font(.caption)
                        }
                    }
                    .tag(season)
                }
            }
            .pickerStyle(.menu)
            .padding(.horizontal)
            .onChange(of: selectedSeason) { _, _ in
                Task {
                    await checkSyncStatus()
                }
            }
        }
    }
    
    @ViewBuilder
    var syncStatusView: some View {
        // Sync Status
        if isAlreadySynced {
            HStack {
                Image(systemName: "checkmark.circle.fill")
                    .foregroundColor(.green)
                Text("\(selectedLeague.name) \(selectedSeason) is already synchronized")
                    .font(.subheadline)
                    .foregroundColor(.green)
            }
            .padding()
            .background(Color.green.opacity(0.1))
            .cornerRadius(8)
        }
    }
    
    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                // Header
                VStack(spacing: 8) {
                    Text("League & Season Sync")
                        .font(.title2)
                        .fontWeight(.bold)
                    
                    Text("Select a league and season to synchronize data")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                }
                .padding()
                
                leagueSelectionView
                seasonSelectionView
                syncStatusView
                
                // Force Refresh Option
                VStack(alignment: .leading, spacing: 8) {
                    Toggle("Force Refresh", isOn: $forceRefresh)
                        .padding(.horizontal)
                    
                    Text("Force refresh will re-synchronize data even if it already exists")
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .padding(.horizontal)
                }
                
                // Progress
                if syncService.isLoading {
                    VStack(spacing: 12) {
                        ProgressView(value: syncService.syncProgress)
                            .padding(.horizontal)
                        
                        Text(syncService.syncStatus)
                            .font(.subheadline)
                            .multilineTextAlignment(.center)
                            .padding(.horizontal)
                    }
                    .padding()
                    .background(Color(.systemGray))
                    .cornerRadius(12)
                    .padding(.horizontal)
                }
                
                // Error Message
                if let errorMessage = syncService.errorMessage {
                    Text(errorMessage)
                        .font(.caption)
                        .foregroundColor(.red)
                        .multilineTextAlignment(.center)
                        .padding()
                        .background(Color.red.opacity(0.1))
                        .cornerRadius(8)
                        .padding(.horizontal)
                }
                
                Spacer()
                
                // Action Buttons
                VStack(spacing: 12) {
                    Button(action: {
                        Task {
                            await syncSelectedLeagueSeason()
                        }
                    }) {
                        Label("Sync \(selectedLeague.name) \(selectedSeason)", systemImage: "arrow.clockwise")
                            .frame(maxWidth: .infinity)
                    }
                    .buttonStyle(.borderedProminent)
                    .disabled(syncService.isLoading)
                    .padding(.horizontal)
                    
                    Button("Cancel") {
                        dismiss()
                    }
                    .buttonStyle(.bordered)
                    .padding(.horizontal)
                }
            }
            .onAppear {
                Task {
                    await loadAvailableSeasons()
                    await checkSyncStatus()
                }
            }
            .alert("Sync Result", isPresented: $showingResults) {
                Button("OK") {
                    if syncService.lastSyncResult?.success == true {
                        dismiss()
                    }
                }
            } message: {
                if let result = syncService.lastSyncResult {
                    Text(result.displayMessage)
                }
            }
        }
    }
    
    private func loadAvailableSeasons() async {
        availableSeasons = await syncService.getAvailableSeasons(for: selectedLeague)
        
        // Update selected season if current one is not available
        if !availableSeasons.contains(selectedSeason), let firstAvailable = availableSeasons.first {
            selectedSeason = firstAvailable
        }
    }
    
    private func checkSyncStatus() async {
        let requests = availableSeasons.map { season in
            LeagueSeasonSyncService.SyncRequest(
                league: selectedLeague,
                season: season,
                forceRefresh: false
            )
        }
        
        syncStatus = await syncService.getSyncStatus(for: requests)
    }
    
    private func syncSelectedLeagueSeason() async {
        let result = await syncService.syncLeagueSeason(
            league: selectedLeague,
            season: selectedSeason,
            forceRefresh: forceRefresh
        )
        
        // Update sync status
        await checkSyncStatus()
        
        // Show result
        showingResults = true
    }
}

struct LeagueCard: View {
    let league: League
    let isSelected: Bool
    let onTap: () -> Void
    
    var body: some View {
        Button(action: onTap) {
            VStack(spacing: 8) {
                // League flag/emblem placeholder
                Image(systemName: "flag.fill")
                    .font(.title2)
                    .foregroundColor(isSelected ? .white : .blue)
                
                VStack(spacing: 2) {
                    Text(league.name)
                        .font(.headline)
                        .fontWeight(.medium)
                        .multilineTextAlignment(.center)
                    
                    Text(league.country)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                .foregroundColor(isSelected ? .white : .primary)
            }
            .frame(width: 120, height: 100)
            .background(isSelected ? Color.blue : Color(.systemGray))
            .cornerRadius(12)
            .overlay(
                RoundedRectangle(cornerRadius: 12)
                    .stroke(isSelected ? Color.blue : Color.clear, lineWidth: 2)
            )
        }
        .buttonStyle(.plain)
    }
}

#Preview {
    LeagueSeasonSyncView()
}
