//
//  FootballBarRaceView.swift
//  PimstatsDashboard
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 15.06.2025.
//

import SwiftUI
import Charts
import Combine

// MARK: - Team Logo View
struct TeamLogo: View {
    let teamName: String
    let teamLogos: [String: String]

    var body: some View {
        if let logoURL = teamLogos[teamName],
           let url = URL(string: logoURL) {
            AsyncImage(url: url) { image in
                image
                    .resizable()
                    .aspectRatio(contentMode: .fit)
            } placeholder: {
                // Fallback to colored circle while loading
                TeamCircle(teamName: teamName)
            }
            .frame(width: 16, height: 16)
            .clipShape(Circle())
        } else {
            // Fallback to colored circle if no logo URL
            TeamCircle(teamName: teamName)
        }
    }
}

struct TeamCircle: View {
    let teamName: String

    var body: some View {
        let primaryColor = getTeamColor(teamName)
        let style = teamStyles[teamName] ?? TeamStyle(primaryColor: primaryColor, secondaryColor: nil, pattern: "solid")

        getTeamFill(style: style)
            .frame(width: 12, height: 12)
            .overlay(
                Circle()
                    .stroke(Color.black.opacity(0.3), lineWidth: 0.5)
            )
    }

    @ViewBuilder
    private func getTeamFill(style: TeamStyle) -> some View {
        switch style.pattern {
        case "gradient":
            if let secondary = style.secondaryColor {
                Circle()
                    .fill(LinearGradient(
                        colors: [style.primaryColor, secondary],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    ))
            } else {
                Circle()
                    .fill(style.primaryColor)
            }
        case "stripe":
            // For stripes, we'll use a radial gradient to simulate the effect
            if let secondary = style.secondaryColor {
                Circle()
                    .fill(RadialGradient(
                        colors: [style.primaryColor, secondary, style.primaryColor],
                        center: .center,
                        startRadius: 2,
                        endRadius: 6
                    ))
            } else {
                Circle()
                    .fill(style.primaryColor)
            }
        default: // "solid"
            Circle()
                .fill(style.primaryColor)
        }
    }
}

// MARK: - Firebase-Integrated Bar Race View
struct FootballBarRaceView: View {
    @StateObject private var dataManager = FirebaseDataManager()
    @State private var bars: [Entry] = []
    @State private var frameIndex = 0
    @State private var isAnimating = false
    
    @State private var cancellable: AnyCancellable? = nil
    
    var bar: some View {
        Chart {
            ForEach(bars.sorted { $0.value > $1.value }) { entry in
                BarMark(
                    x: .value("Points", entry.value),
                    y: .value("Team", entry.id),
                    height: .fixed(8) // Thin bars like lines
                )
                .annotation(position: .trailing) {
                    HStack(spacing: 6) {
                        TeamLogo(teamName: entry.id, teamLogos: dataManager.teamLogos)
                        Text(Int(entry.value).formatted())
                            .font(.caption.bold())
                            .foregroundColor(.primary)
                    }
                }
                .annotation(position: .topLeading) {
                    Text(getShortTeamName(entry.id))
                        .font(.caption.bold())
                        .foregroundColor(.primary)
                }
                .foregroundStyle(getTeamColor(entry.id))
            }
        }
        .chartXAxis(.hidden)
        .chartYAxis(.hidden) // Hide Y-axis to remove separators
        .chartPlotStyle { plotArea in
            plotArea
                .background(.clear) // Remove background grid
        }
        .frame(height: min(CGFloat(bars.count * 35 + 50), 500))
        .animation(.easeInOut(duration: 1.5), value: bars)
    }

    var body: some View {
        VStack(spacing: 24) {
            // League and Season Selection
            VStack(spacing: 16) {
                HStack {
                    VStack(alignment: .leading) {
                        Text("League")
                            .font(.caption)
                            .foregroundColor(.secondary)
                        
                        Picker("League", selection: $dataManager.selectedLeague) {
                            ForEach(League.supportedLeagues, id: \.id) { league in
                                Text(league.name).tag(league)
                            }
                        }
                        .pickerStyle(.menu)
                    }
                    
                    Spacer()
                    
                    VStack(alignment: .trailing) {
                        Text("Season")
                            .font(.caption)
                            .foregroundColor(.secondary)
                        
                        Picker("Season", selection: $dataManager.selectedSeason) {
                            ForEach(FootballAPIConfig.availableSeasons, id: \.self) { season in
                                Text("\(season)/\(String(season + 1).suffix(2))").tag(season)
                            }
                        }
                        .pickerStyle(.menu)
                    }
                }
                .padding(.horizontal)
            }
            
            if dataManager.isLoading {
                ProgressView("Loading football data...")
                    .font(.headline)
                    .padding()
            } else if let errorMessage = dataManager.errorMessage {
                VStack(spacing: 16) {
                    Text("Error loading data")
                        .font(.headline)
                        .foregroundColor(.red)
                    Text(errorMessage)
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                    Button("Retry") {
                        Task {
                            await dataManager.loadData()
                        }
                    }
                    .buttonStyle(.borderedProminent)
                }
                .padding()
            } else if !bars.isEmpty {
                bar

                // Matchday Label
                VStack(spacing: 8) {
                    if frameIndex < dataManager.matchdays.count {
                        Text("Matchday \(dataManager.matchdays[frameIndex])")
                            .font(.largeTitle.weight(.semibold))
                            .transition(.opacity.combined(with: .scale))
                            .id(frameIndex)
                    }

                    Text("\(dataManager.selectedLeague.name) \(dataManager.selectedSeason)")
                        .font(.title3.weight(.medium))
                        .foregroundColor(.secondary)
                }
                
                // Control buttons
                HStack(spacing: 16) {
                    Button(isAnimating ? "Pause" : "Play") {
                        resetTicker()
                        isAnimating.toggle()
                    }
                    .padding(.horizontal, 20)
                    .padding(.vertical, 10)
                    .background(isAnimating ? Color.orange : Color.blue)
                    .foregroundColor(.white)
                    .cornerRadius(8)
                    
                    Button("Reset") {
                        cancelTicker()
                        isAnimating = false
                        frameIndex = 0
                        updateBarsForCurrentFrame()
                    }
                    .padding(.horizontal, 20)
                    .padding(.vertical, 10)
                    .background(Color.gray)
                    .foregroundColor(.white)
                    .cornerRadius(8)
                }
            } else {
                VStack(spacing: 16) {
                    Text("No data available")
                        .font(.headline)
                        .foregroundColor(.secondary)
                    
                    Text("Select a league and season with synchronized data to see the animated bar race")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                    
                    Button("Load Data") {
                        Task {
                            await dataManager.loadData()
                        }
                    }
                    .buttonStyle(.borderedProminent)
                }
                .padding()
            }
        }
        .padding(.vertical, 32)
        .navigationTitle("Bar Race")
        .onAppear {
            Task {
                await dataManager.loadData()
            }
        }
        .onChange(of: dataManager.selectedLeague) { _, _ in
            Task {
                await dataManager.loadData()
            }
        }
        .onChange(of: dataManager.selectedSeason) { _, _ in
            Task {
                await dataManager.loadData()
            }
        }
        .onChange(of: dataManager.matchdays) { _, _ in
            updateBarsForCurrentFrame()
        }
    }
    
    private func resetTicker() {
        if cancellable != nil {
            cancelTicker()
        } else {
            cancellable = Timer.publish(every: 2.0, on: .main, in: .common).autoconnect().sink { _ in
                if isAnimating {
                    advanceFrame()
                }
            }
        }
    }
    
    private func cancelTicker() {
        if cancellable != nil {
            cancellable?.cancel()
            cancellable = nil
        }
    }

    // MARK: - Private Methods

    // Advance one matchday → update entries with new points
    private func advanceFrame() {
        guard !dataManager.matchdays.isEmpty else { return }
        let newFrameIndex = (frameIndex + 1)
        if newFrameIndex >= dataManager.matchdays.count {
            frameIndex = 0
        } else {
            frameIndex = newFrameIndex
            updateBarsForCurrentFrame()
        }
    }

    private func updateBarsForCurrentFrame() {
        guard frameIndex < dataManager.matchdays.count else { return }
        let currentMatchday = dataManager.matchdays[frameIndex]
        bars = dataManager.getPointsForMatchday(currentMatchday)
    }
}

#Preview {
    NavigationView {
        FootballBarRaceView()
    }
}
