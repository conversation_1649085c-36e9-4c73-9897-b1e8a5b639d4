//
//  FootballDashboardView.swift
//  PimstatsDashboard
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 15.06.2025.
//

import SwiftUI

struct FootballDashboardView: View {
    @StateObject private var coordinator = FootballDataCoordinator()
    @StateObject private var firebaseService = FirebaseService()
    @State private var selectedSeason: Int = 2024
    @State private var availableSeasons: [Int] = FootballAPIConfig.availableSeasons
    @State private var syncStatus: [Int: Bool] = [:]
    @State private var showingSyncView = false
    @State private var showingLeagueSeasonSync = false
    @State private var selectedLeague: League = League.premierLeague
    @State private var teams: [FirebaseTeam] = []
    @State private var seasonPerformances: [TeamSeasonPerformance] = []
    @State private var showingDataSourceSelection = false
    
    var body: some View {
        NavigationSplitView {
            // Sidebar
            VStack(alignment: .leading, spacing: 20) {
                // Header
                VStack(alignment: .leading, spacing: 8) {
                    Text("Football Dashboard")
                        .font(.title2)
                        .fontWeight(.bold)

                    Text("\(selectedLeague.name) Statistics")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                .padding(.horizontal)

                // Data Source Button
                Button(action: {
                    showingDataSourceSelection = true
                }) {
                    HStack {
                        Label(
                            coordinator.dataSource == .footballDataAPI ? 
                            "Football-data.org API" : "GitHub Repository",
                            systemImage: "server.rack"
                        )
                        Spacer()
                        Image(systemName: "chevron.right")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    .padding(.vertical, 8)
                    .padding(.horizontal)
                    .background(Color(.secondarySystemBackground))
                    .cornerRadius(8)
                }
                .buttonStyle(.plain)
                .padding(.horizontal)
                
                // League Selection
                VStack(alignment: .leading, spacing: 8) {
                    Text("League")
                        .font(.headline)
                        .padding(.horizontal)

                    Picker("League", selection: $selectedLeague) {
                        ForEach(League.supportedLeagues, id: \.id) { league in
                            Text(league.name).tag(league)
                        }
                    }
                    .pickerStyle(.menu)
                    .padding(.horizontal)
                }
                
                Divider()
                
                // Season Selection
                VStack(alignment: .leading, spacing: 12) {
                    Text("Season")
                        .font(.headline)
                        .padding(.horizontal)
                    
                    Picker("Season", selection: $selectedSeason) {
                        ForEach(availableSeasons, id: \.self) { season in
                            HStack {
                                Text("\(season)/\(String(season + 1).suffix(2))")
                                Spacer()
                                if syncStatus[season] == true {
                                    Image(systemName: "checkmark.circle.fill")
                                        .foregroundColor(.green)
                                        .font(.caption)
                                }
                            }
                            .tag(season)
                        }
                    }
                    .pickerStyle(.menu)
                    .padding(.horizontal)
                }
                
                Divider()
                
                // Navigation Links
                VStack(alignment: .leading, spacing: 8) {
                    NavigationLink(destination: TeamsListView(teams: teams)) {
                        Label("Teams", systemImage: "person.3.fill")
                    }
                    .padding(.horizontal)
                    
                    NavigationLink(destination: SeasonPerformanceView(
                        performances: seasonPerformances,
                        season: selectedSeason
                    )) {
                        Label("Season Performance", systemImage: "chart.line.uptrend.xyaxis")
                    }
                    .padding(.horizontal)
                    
                    NavigationLink(destination: MatchdayProgressView(
                        performances: seasonPerformances,
                        season: selectedSeason
                    )) {
                        Label("Matchday Progress", systemImage: "calendar")
                    }
                    .padding(.horizontal)

                    NavigationLink(destination: SyncSummaryView()) {
                        Label("Sync Summary", systemImage: "list.bullet.clipboard")
                    }
                    .padding(.horizontal)

                    NavigationLink(destination: FootballBarRaceView()) {
                        Label("Bar Race Animation", systemImage: "chart.bar.xaxis")
                    }
                    .padding(.horizontal)
                }
                
                Spacer()
                
                // Sync Controls
                VStack(spacing: 12) {
                    Button(action: {
                        showingLeagueSeasonSync = true
                    }) {
                        Label("Sync League & Season", systemImage: "plus.circle")
                            .frame(maxWidth: .infinity)
                    }
                    .buttonStyle(.borderedProminent)
                    .padding(.horizontal)

                    Button(action: {
                        showingSyncView = true
                    }) {
                        Label("Bulk Sync", systemImage: "arrow.clockwise")
                            .frame(maxWidth: .infinity)
                    }
                    .buttonStyle(.bordered)
                    .padding(.horizontal)
                    
                    if coordinator.isLoading {
                        VStack(spacing: 4) {
                            ProgressView(value: coordinator.syncProgress)
                                .padding(.horizontal)
                            
                            Text(coordinator.syncStatus)
                                .font(.caption)
                                .foregroundColor(.secondary)
                                .padding(.horizontal)
                        }
                    }
                }
            }
            .navigationSplitViewColumnWidth(min: 250, ideal: 300)
            .onAppear {
                Task {
                    await loadInitialData()
                }
            }
            .onChange(of: selectedSeason) { _, newSeason in
                Task {
                    await loadSeasonData(for: newSeason, league: selectedLeague.id)
                }
            }
        } detail: {
            // Main Content
            if seasonPerformances.isEmpty && !coordinator.isLoading {
                ContentUnavailableView(
                    "No Data Available",
                    systemImage: "chart.bar.xaxis",
                    description: Text("Select a season and sync data to get started")
                )
            } else {
                SeasonOverviewView(
                    performances: seasonPerformances,
                    season: selectedSeason
                )
            }
        }
        .sheet(isPresented: $showingSyncView) {
            DataSyncView(coordinator: coordinator, syncStatus: syncStatus)
        }
        .sheet(isPresented: $showingLeagueSeasonSync) {
            LeagueSeasonSyncView()
        }
        .sheet(isPresented: $showingDataSourceSelection) {
            NavigationView {
                DataSourceSelectionView(coordinator: coordinator)
                    .navigationTitle("Data Source")
                    .toolbar {
                        ToolbarItem(placement: .confirmationAction) {
                            Button("Done") {
                                showingDataSourceSelection = false
                            }
                        }
                    }
            }
        }
        .onChange(of: selectedLeague) { _, newLeague in
            Task {
                await loadLeagueData(for: newLeague)
            }
        }
        .toolbar {
            ToolbarItem(placement: .navigationBarTrailing) {
                Menu {
                    Button("Football-data.org API") {
                        Task {
                            await coordinator.switchDataSource(to: .footballDataAPI)
                        }
                    }
                    .disabled(coordinator.dataSource == .footballDataAPI)
                    
                    Button("GitHub Repository") {
                        Task {
                            await coordinator.switchDataSource(to: .githubRepository)
                        }
                    }
                    .disabled(coordinator.dataSource == .githubRepository)
                    
                    Divider()
                    
                    NavigationLink(destination: DataSourceSelectionView(coordinator: coordinator)) {
                        Label("Advanced Settings", systemImage: "gear")
                    }
                } label: {
                    Label("Data Source", systemImage: "server.rack")
                }
            }
        }
    }
    
    // MARK: - Private Methods
    
    private func loadInitialData() async {
        // Check sync status for all seasons
        syncStatus = await coordinator.checkSyncStatus()

        // Load data for selected league and season
        await loadLeagueData(for: selectedLeague)
    }

    private func loadLeagueData(for league: League) async {
        // Load teams for the selected league
        do {
            teams = try await firebaseService.fetchTeams(for: league.id)
        } catch {
            print("Error loading teams for \(league.name): \(error)")
            teams = []
        }

        // Load data for selected season
        await loadSeasonData(for: selectedSeason, league: league.id)
    }
    
    private func loadSeasonData(for season: Int, league: String = "PL") async {
        do {
            seasonPerformances = try await firebaseService.fetchTeamSeasonPerformances(for: season, league: league)
        } catch {
            print("Error loading season data for \(league) \(season): \(error)")
            seasonPerformances = []
        }
    }
}

// MARK: - Season Overview View
struct SeasonOverviewView: View {
    let performances: [TeamSeasonPerformance]
    let season: Int
    
    var body: some View {
        ScrollView {
            LazyVStack(spacing: 20) {
                // Season Header
                VStack(alignment: .leading, spacing: 8) {
                    Text("Season \(season)/\(String(season + 1).suffix(2))")
                        .font(.largeTitle)
                        .fontWeight(.bold)
                    
                    Text("\(performances.count) teams")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
                .frame(maxWidth: .infinity, alignment: .leading)
                .padding()
                
                // Quick Stats
                if !performances.isEmpty {
                    LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 3), spacing: 16) {
                        StatCard(
                            title: "Total Teams",
                            value: "\(performances.count)",
                            icon: "person.3.fill"
                        )
                        
                        StatCard(
                            title: "Avg Matchdays",
                            value: "\(averageMatchdays)",
                            icon: "calendar"
                        )
                        
                        StatCard(
                            title: "Total Goals",
                            value: "\(totalGoals)",
                            icon: "soccerball"
                        )
                    }
                    .padding(.horizontal)
                    
                    // Top Teams
                    VStack(alignment: .leading, spacing: 12) {
                        Text("Top Teams")
                            .font(.headline)
                            .padding(.horizontal)
                        
                        LazyVStack(spacing: 8) {
                            ForEach(topTeams.prefix(5), id: \.teamId) { performance in
                                TeamPerformanceRow(performance: performance)
                            }
                        }
                        .padding(.horizontal)
                    }
                }
            }
        }
        .navigationTitle("Dashboard")
    }
    
    private var averageMatchdays: Int {
        guard !performances.isEmpty else { return 0 }
        let totalMatchdays = performances.reduce(0) { $0 + $1.matchdayPerformances.count }
        return totalMatchdays / performances.count
    }
    
    private var totalGoals: Int {
        performances.reduce(0) { $0 + $1.totalGoalsFor }
    }
    
    private var topTeams: [TeamSeasonPerformance] {
        performances.sorted { $0.totalPoints > $1.totalPoints }
    }
}

// MARK: - Stat Card Component
struct StatCard: View {
    let title: String
    let value: String
    let icon: String
    
    var body: some View {
        VStack(spacing: 8) {
            Image(systemName: icon)
                .font(.title2)
                .foregroundColor(.blue)
            
            Text(value)
                .font(.title2)
                .fontWeight(.bold)
            
            Text(title)
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity)
        .padding()
        .background(Color(.systemGray))
        .cornerRadius(12)
    }
}

// MARK: - Team Performance Row Component
struct TeamPerformanceRow: View {
    let performance: TeamSeasonPerformance
    
    var body: some View {
        HStack {
            VStack(alignment: .leading, spacing: 4) {
                Text(performance.teamName)
                    .font(.headline)
                
                Text("\(performance.matchdayPerformances.count) matchdays")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
            
            VStack(alignment: .trailing, spacing: 4) {
                Text("\(performance.totalPoints) pts")
                    .font(.headline)
                    .fontWeight(.bold)
                
                Text("GD: \(performance.totalGoalDifference > 0 ? "+" : "")\(performance.totalGoalDifference)")
                    .font(.caption)
                    .foregroundColor(performance.totalGoalDifference >= 0 ? .green : .red)
            }
        }
        .padding()
        .background(Color(.systemGray))
        .cornerRadius(8)
        .shadow(radius: 1)
    }
}

#Preview {
    FootballDashboardView()
}
