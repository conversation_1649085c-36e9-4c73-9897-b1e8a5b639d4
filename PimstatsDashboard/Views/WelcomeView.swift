//
//  WelcomeView.swift
//  PimstatsDashboard
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 15.06.2025.
//

import SwiftUI

struct WelcomeView: View {
    @State private var showingSetup = false
    
    var body: some View {
        VStack(spacing: 30) {
            // Header
            VStack(spacing: 16) {
                Image(systemName: "soccerball")
                    .font(.system(size: 80))
                    .foregroundColor(.blue)
                
                Text("Pimstats Dashboard")
                    .font(.largeTitle)
                    .fontWeight(.bold)
                
                Text("Football Statistics & Analytics")
                    .font(.title2)
                    .foregroundColor(.secondary)
            }
            
            // Description
            VStack(spacing: 12) {
                Text("Welcome to your football statistics dashboard!")
                    .font(.headline)
                    .multilineTextAlignment(.center)
                
                Text("This app integrates with the Football Data API to fetch Premier League statistics and stores them in Firebase for analysis and visualization.")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
                    .padding(.horizontal)
            }
            
            // Features
            VStack(alignment: .leading, spacing: 12) {
                Text("Features:")
                    .font(.headline)
                
                FeatureRow(icon: "arrow.down.circle", title: "API Integration", description: "Fetch live data from Football Data API")
                FeatureRow(icon: "icloud", title: "Firebase Storage", description: "Store and sync data across devices")
                FeatureRow(icon: "chart.bar", title: "Analytics", description: "View team performance and statistics")
                FeatureRow(icon: "calendar", title: "Season Tracking", description: "Track progress across matchdays")
            }
            .padding()
            .background(Color(.systemGray))
            .cornerRadius(12)
            .padding(.horizontal)
            
            Spacer()
            
            // Action Buttons
            VStack(spacing: 12) {
                Button(action: {
                    showingSetup = true
                }) {
                    Label("Get Started", systemImage: "arrow.right")
                        .frame(maxWidth: .infinity)
                }
                .buttonStyle(.borderedProminent)
                .controlSize(.large)
                
                Button(action: {
                    // Test API without Firebase
                    testAPIOnly()
                }) {
                    Label("Test API Only", systemImage: "network")
                        .frame(maxWidth: .infinity)
                }
                .buttonStyle(.bordered)
            }
            .padding(.horizontal)
        }
        .padding()
        .navigationTitle("Welcome")
        .sheet(isPresented: $showingSetup) {
            NavigationView {
                FirebaseSetupView()
            }
        }
    }
    
    private func testAPIOnly() {
        Task {
            do {
                let apiService = FootballAPIService()
                let teams = try await apiService.fetchTeams(for: 2024)
                print("✅ API Test Success: Found \(teams.count) teams")
                
                // Show success alert
                DispatchQueue.main.async {
                    // You could show an alert here
                    print("API test completed successfully!")
                }
            } catch {
                print("❌ API Test Failed: \(error)")
            }
        }
    }
}

struct FeatureRow: View {
    let icon: String
    let title: String
    let description: String
    
    var body: some View {
        HStack(spacing: 12) {
            Image(systemName: icon)
                .font(.title2)
                .foregroundColor(.blue)
                .frame(width: 30)
            
            VStack(alignment: .leading, spacing: 2) {
                Text(title)
                    .font(.headline)
                
                Text(description)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
        }
    }
}

#Preview {
    NavigationView {
        WelcomeView()
    }
}
