//
//  SyncSummaryView.swift
//  PimstatsDashboard
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 15.06.2025.
//

import SwiftUI

struct SyncSummaryView: View {
    @StateObject private var firebaseService = FirebaseService()
    @State private var syncedData: [SyncedDataInfo] = []
    @State private var isLoading = true
    
    struct SyncedDataInfo {
        let league: String
        let season: Int
        let teamsCount: Int
        let lastUpdated: Date
        
        var displayName: String {
            let leagueName = League.supportedLeagues.first { $0.id == league }?.name ?? league
            return "\(leagueName) \(season)/\(String(season + 1).suffix(2))"
        }
    }
    
    var body: some View {
        NavigationView {
            VStack {
                if isLoading {
                    ProgressView("Loading synced data...")
                        .frame(maxWidth: .infinity, maxHeight: .infinity)
                } else if syncedData.isEmpty {
                    ContentUnavailableView(
                        "No Synced Data",
                        systemImage: "tray.fill",
                        description: Text("No league/season data has been synchronized yet")
                    )
                } else {
                    List {
                        Section("Synchronized Data") {
                            ForEach(syncedData.indices, id: \.self) { index in
                                let data = syncedData[index]
                                SyncedDataRow(data: data)
                            }
                        }
                        
                        Section("Statistics") {
                            HStack {
                                Text("Total Leagues")
                                Spacer()
                                Text("\(uniqueLeagues.count)")
                                    .foregroundColor(.secondary)
                            }
                            
                            HStack {
                                Text("Total Seasons")
                                Spacer()
                                Text("\(syncedData.count)")
                                    .foregroundColor(.secondary)
                            }
                            
                            HStack {
                                Text("Total Teams")
                                Spacer()
                                Text("\(totalTeams)")
                                    .foregroundColor(.secondary)
                            }
                        }
                    }
                }
            }
            .navigationTitle("Sync Summary")
            .refreshable {
                await loadSyncedData()
            }
            .onAppear {
                Task {
                    await loadSyncedData()
                }
            }
        }
    }
    
    private var uniqueLeagues: Set<String> {
        Set(syncedData.map { $0.league })
    }
    
    private var totalTeams: Int {
        syncedData.reduce(0) { $0 + $1.teamsCount }
    }
    
    private func loadSyncedData() async {
        isLoading = true
        
        do {
            // Get all season summaries
            var allSummaries: [SeasonSummary] = []
            
            for league in League.supportedLeagues {
                let summaries = try await firebaseService.fetchSeasonSummaries(for: league.id)
                allSummaries.append(contentsOf: summaries)
            }
            
            // Convert to display format
            let syncedInfo = allSummaries.map { summary in
                SyncedDataInfo(
                    league: summary.league,
                    season: summary.season,
                    teamsCount: summary.totalTeams,
                    lastUpdated: summary.lastUpdated
                )
            }
            
            // Sort by league and season
            syncedData = syncedInfo.sorted { first, second in
                if first.league == second.league {
                    return first.season > second.season
                }
                return first.league < second.league
            }
            
        } catch {
            print("Error loading synced data: \(error)")
            syncedData = []
        }
        
        isLoading = false
    }
}

struct SyncedDataRow: View {
    let data: SyncSummaryView.SyncedDataInfo
    
    var body: some View {
        HStack {
            VStack(alignment: .leading, spacing: 4) {
                Text(data.displayName)
                    .font(.headline)
                
                Text("\(data.teamsCount) teams")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
            
            VStack(alignment: .trailing, spacing: 4) {
                Text("Synced")
                    .font(.caption)
                    .foregroundColor(.green)
                
                Text(data.lastUpdated, style: .date)
                    .font(.caption2)
                    .foregroundColor(.secondary)
            }
        }
        .padding(.vertical, 2)
    }
}

#Preview {
    SyncSummaryView()
}
