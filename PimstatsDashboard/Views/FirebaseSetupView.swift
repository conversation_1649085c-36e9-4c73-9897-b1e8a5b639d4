//
//  FirebaseSetupView.swift
//  PimstatsDashboard
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 15.06.2025.
//

import SwiftUI
import FirebaseFirestore

struct FirebaseSetupView: View {
    @State private var setupStatus: SetupStatus = .checking
    @State private var errorMessage: String?
    @State private var testResults: [String] = []
    
    enum SetupStatus {
        case checking
        case needsSetup
        case ready
        case error
    }
    
    var body: some View {
        VStack(spacing: 20) {
            // Header
            VStack(spacing: 8) {
                Image(systemName: setupStatus.icon)
                    .font(.system(size: 60))
                    .foregroundColor(setupStatus.color)
                
                Text("Firebase Setup")
                    .font(.title)
                    .fontWeight(.bold)
                
                Text(setupStatus.message)
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }
            .padding()
            
            // Setup Instructions
            if setupStatus == .needsSetup {
                VStack(alignment: .leading, spacing: 16) {
                    Text("Required Setup Steps:")
                        .font(.headline)
                        .padding(.horizontal)
                    
                    VStack(alignment: .leading, spacing: 12) {
                        SetupStepView(
                            number: 1,
                            title: "Enable Firestore API",
                            description: "Visit Google Cloud Console and enable the Firestore API",
                            actionTitle: "Open Google Cloud Console",
                            action: {
                                if let url = URL(string: "https://console.developers.google.com/apis/api/firestore.googleapis.com/overview?project=pimstats-3b9a6") {
                                    NSWorkspace.shared.open(url)
                                }
                            }
                        )
                        
                        SetupStepView(
                            number: 2,
                            title: "Create Firestore Database",
                            description: "Go to Firebase Console and create a Firestore database in test mode",
                            actionTitle: "Open Firebase Console",
                            action: {
                                if let url = URL(string: "https://console.firebase.google.com/project/pimstats-3b9a6/firestore") {
                                    NSWorkspace.shared.open(url)
                                }
                            }
                        )
                        
                        SetupStepView(
                            number: 3,
                            title: "Add GoogleService-Info.plist",
                            description: "Download and add GoogleService-Info.plist to your Xcode project",
                            actionTitle: "Download Config File",
                            action: {
                                if let url = URL(string: "https://console.firebase.google.com/project/pimstats-3b9a6/settings/general") {
                                    NSWorkspace.shared.open(url)
                                }
                            }
                        )
                    }
                    .padding(.horizontal)
                }
            }
            
            // Test Results
            if !testResults.isEmpty {
                VStack(alignment: .leading, spacing: 8) {
                    Text("Test Results:")
                        .font(.headline)
                        .padding(.horizontal)
                    
                    ScrollView {
                        VStack(alignment: .leading, spacing: 4) {
                            ForEach(testResults, id: \.self) { result in
                                Text(result)
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                            }
                        }
                        .padding(.horizontal)
                    }
                    .frame(maxHeight: 150)
                }
            }
            
            // Error Message
            if let errorMessage = errorMessage {
                Text(errorMessage)
                    .font(.caption)
                    .foregroundColor(.red)
                    .multilineTextAlignment(.center)
                    .padding()
                    .background(Color.red.opacity(0.1))
                    .cornerRadius(8)
                    .padding(.horizontal)
            }
            
            Spacer()
            
            // Action Buttons
            VStack(spacing: 12) {
                Button(action: {
                    Task {
                        await checkFirebaseSetup()
                    }
                }) {
                    Label("Test Firebase Connection", systemImage: "arrow.clockwise")
                        .frame(maxWidth: .infinity)
                }
                .buttonStyle(.borderedProminent)
                
                if setupStatus == .ready {
                    NavigationLink(destination: SystemTestView()) {
                        Label("Continue to System Test", systemImage: "arrow.right")
                            .frame(maxWidth: .infinity)
                    }
                    .buttonStyle(.bordered)
                }
            }
            .padding(.horizontal)
        }
        .navigationTitle("Firebase Setup")
        .onAppear {
            Task {
                await checkFirebaseSetup()
            }
        }
    }
    
    private func checkFirebaseSetup() async {
        setupStatus = .checking
        errorMessage = nil
        testResults = []
        
        // Test 1: Check if GoogleService-Info.plist exists
        testResults.append("🔍 Checking GoogleService-Info.plist...")
        guard let path = Bundle.main.path(forResource: "GoogleService-Info", ofType: "plist") else {
            setupStatus = .needsSetup
            errorMessage = "GoogleService-Info.plist not found in project"
            testResults.append("❌ GoogleService-Info.plist not found")
            return
        }
        testResults.append("✅ GoogleService-Info.plist found")
        
        // Test 2: Try to access Firestore
        testResults.append("🔍 Testing Firestore connection...")
        do {
            let db = Firestore.firestore()
            let testCollection = db.collection("test")
            let _ = try await testCollection.limit(to: 1).getDocuments()
            testResults.append("✅ Firestore connection successful")
            setupStatus = .ready
        } catch {
            testResults.append("❌ Firestore connection failed: \(error.localizedDescription)")
            
            if error.localizedDescription.contains("Cloud Firestore API has not been used") {
                setupStatus = .needsSetup
                errorMessage = "Firestore API needs to be enabled in Google Cloud Console"
            } else if error.localizedDescription.contains("PERMISSION_DENIED") {
                setupStatus = .needsSetup
                errorMessage = "Firestore database needs to be created in Firebase Console"
            } else {
                setupStatus = .error
                errorMessage = "Firebase setup error: \(error.localizedDescription)"
            }
        }
    }
}

extension FirebaseSetupView.SetupStatus {
    var icon: String {
        switch self {
        case .checking: return "clock.fill"
        case .needsSetup: return "exclamationmark.triangle.fill"
        case .ready: return "checkmark.circle.fill"
        case .error: return "xmark.circle.fill"
        }
    }
    
    var color: Color {
        switch self {
        case .checking: return .blue
        case .needsSetup: return .orange
        case .ready: return .green
        case .error: return .red
        }
    }
    
    var message: String {
        switch self {
        case .checking: return "Checking Firebase configuration..."
        case .needsSetup: return "Firebase setup required"
        case .ready: return "Firebase is ready to use!"
        case .error: return "Firebase setup error"
        }
    }
}

struct SetupStepView: View {
    let number: Int
    let title: String
    let description: String
    let actionTitle: String
    let action: () -> Void
    
    var body: some View {
        HStack(alignment: .top, spacing: 12) {
            Text("\(number)")
                .font(.headline)
                .fontWeight(.bold)
                .foregroundColor(.white)
                .frame(width: 24, height: 24)
                .background(Color.blue)
                .clipShape(Circle())
            
            VStack(alignment: .leading, spacing: 8) {
                Text(title)
                    .font(.headline)
                
                Text(description)
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                
                Button(actionTitle) {
                    action()
                }
                .buttonStyle(.bordered)
                .controlSize(.small)
            }
            
            Spacer()
        }
        .padding()
        .background(Color(.systemGray))
        .cornerRadius(12)
    }
}

#Preview {
    NavigationView {
        FirebaseSetupView()
    }
}
