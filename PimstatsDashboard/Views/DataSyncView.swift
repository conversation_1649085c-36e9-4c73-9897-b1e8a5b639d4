//
//  DataSyncView.swift
//  PimstatsDashboard
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 15.06.2025.
//

import SwiftUI

// MARK: - Season Performance View
struct SeasonPerformanceView: View {
    let performances: [TeamSeasonPerformance]
    let season: Int

    var sortedPerformances: [TeamSeasonPerformance] {
        performances.sorted { $0.totalPoints > $1.totalPoints }
    }

    var body: some View {
        List {
            ForEach(Array(sortedPerformances.enumerated()), id: \.element.teamId) { index, performance in
                HStack {
                    // Position
                    Text("\(index + 1)")
                        .font(.headline)
                        .fontWeight(.bold)
                        .frame(width: 30)
                        .foregroundColor(.secondary)

                    // Team Info
                    VStack(alignment: .leading, spacing: 4) {
                        Text(performance.teamName)
                            .font(.headline)

                        Text("\(performance.matchdayPerformances.count) games")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }

                    Spacer()

                    // Stats
                    VStack(alignment: .trailing, spacing: 4) {
                        Text("\(performance.totalPoints)")
                            .font(.headline)
                            .fontWeight(.bold)

                        Text("\(performance.totalGoalsFor):\(performance.totalGoalsAgainst)")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
                .padding(.vertical, 4)
            }
        }
        .navigationTitle("Season \(season)")
    }
}

// MARK: - Matchday Progress View
struct MatchdayProgressView: View {
    let performances: [TeamSeasonPerformance]
    let season: Int
    @State private var selectedMatchday: Int = 1

    var availableMatchdays: [Int] {
        let allMatchdays = performances.flatMap { $0.matchdayPerformances.map { $0.matchday } }
        return Array(Set(allMatchdays)).sorted()
    }

    var matchdayPerformances: [(String, Int)] {
        performances.compactMap { performance in
            guard let matchdayPerf = performance.matchdayPerformances.first(where: { $0.matchday == selectedMatchday }) else {
                return nil
            }
            return (performance.teamName, matchdayPerf.points)
        }.sorted { $0.1 > $1.1 }
    }

    var body: some View {
        VStack {
            // Matchday Picker
            if !availableMatchdays.isEmpty {
                Picker("Matchday", selection: $selectedMatchday) {
                    ForEach(availableMatchdays, id: \.self) { matchday in
                        Text("Matchday \(matchday)").tag(matchday)
                    }
                }
                .pickerStyle(.segmented)
                .padding()

                // Performance List
                List {
                    ForEach(Array(matchdayPerformances.enumerated()), id: \.offset) { index, performance in
                        HStack {
                            Text("\(index + 1)")
                                .font(.headline)
                                .fontWeight(.bold)
                                .frame(width: 30)
                                .foregroundColor(.secondary)

                            Text(performance.0)
                                .font(.headline)

                            Spacer()

                            Text("\(performance.1) pts")
                                .font(.headline)
                                .fontWeight(.bold)
                        }
                        .padding(.vertical, 4)
                    }
                }
            } else {
                ContentUnavailableView(
                    "No Matchday Data",
                    systemImage: "calendar.badge.exclamationmark",
                    description: Text("No matchday data available for this season")
                )
            }
        }
        .navigationTitle("Matchday Progress")
        .onAppear {
            if let firstMatchday = availableMatchdays.first {
                selectedMatchday = firstMatchday
            }
        }
    }
}

struct DataSyncView: View {
    @ObservedObject var dataSyncService: DataSyncService
    let syncStatus: [Int: Bool]
    @Environment(\.dismiss) private var dismiss
    @State private var selectedSeasons: Set<Int> = []
    @State private var forceRefresh = false
    
    var availableSeasons: [Int] {
        FootballAPIConfig.availableSeasons.sorted(by: >)
    }
    
    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                // Header
                VStack(spacing: 8) {
                    Text("Data Synchronization")
                        .font(.title2)
                        .fontWeight(.bold)
                    
                    Text("Sync football data from API to Firebase")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                }
                .padding()
                
                // Season Selection
                VStack(alignment: .leading, spacing: 12) {
                    Text("Select Seasons to Sync")
                        .font(.headline)
                        .padding(.horizontal)
                    
                    ScrollView {
                        LazyVStack(spacing: 8) {
                            ForEach(availableSeasons, id: \.self) { season in
                                SeasonSelectionRow(
                                    season: season,
                                    isSelected: selectedSeasons.contains(season),
                                    isSynced: syncStatus[season] == true,
                                    onToggle: { isSelected in
                                        if isSelected {
                                            selectedSeasons.insert(season)
                                        } else {
                                            selectedSeasons.remove(season)
                                        }
                                    }
                                )
                            }
                        }
                        .padding(.horizontal)
                    }
                }
                
                // Options
                VStack(alignment: .leading, spacing: 12) {
                    Toggle("Force Refresh (Re-sync existing data)", isOn: $forceRefresh)
                        .padding(.horizontal)
                    
                    Text("Force refresh will overwrite existing data for selected seasons")
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .padding(.horizontal)
                }
                
                // Progress
                if dataSyncService.isLoading {
                    VStack(spacing: 12) {
                        ProgressView(value: dataSyncService.syncProgress)
                            .padding(.horizontal)
                        
                        Text(dataSyncService.syncStatus)
                            .font(.subheadline)
                            .multilineTextAlignment(.center)
                            .padding(.horizontal)
                    }
                    .padding()
                    .background(Color(.systemGray))
                    .cornerRadius(12)
                    .padding(.horizontal)
                }
                
                // Error Message
                if let errorMessage = dataSyncService.errorMessage {
                    Text(errorMessage)
                        .font(.caption)
                        .foregroundColor(.red)
                        .multilineTextAlignment(.center)
                        .padding(.horizontal)
                }
                
                Spacer()
                
                // Action Buttons
                VStack(spacing: 12) {
                    Button(action: {
                        Task {
                            await syncSelectedSeasons()
                        }
                    }) {
                        Text("Sync \(selectedSeasons.count) Season\(selectedSeasons.count == 1 ? "" : "s")")
                            .frame(maxWidth: .infinity)
                    }
                    .buttonStyle(.borderedProminent)
                    .disabled(selectedSeasons.isEmpty || dataSyncService.isLoading)
                    .padding(.horizontal)
                    
                    Button("Cancel") {
                        dismiss()
                    }
                    .buttonStyle(.bordered)
                    .padding(.horizontal)
                }
            }
        }
    }
    
    private func syncSelectedSeasons() async {
        let seasons = Array(selectedSeasons).sorted(by: >)
        await dataSyncService.syncMultipleSeasons(seasons, forceRefresh: forceRefresh)
        
        // Auto-dismiss after successful sync
        if dataSyncService.errorMessage == nil {
            DispatchQueue.main.asyncAfter(deadline: .now() + 1) {
                dismiss()
            }
        }
    }
}

struct SeasonSelectionRow: View {
    let season: Int
    let isSelected: Bool
    let isSynced: Bool
    let onToggle: (Bool) -> Void
    
    var body: some View {
        HStack {
            Button(action: {
                onToggle(!isSelected)
            }) {
                HStack {
                    Image(systemName: isSelected ? "checkmark.circle.fill" : "circle")
                        .foregroundColor(isSelected ? .blue : .gray)
                    
                    VStack(alignment: .leading, spacing: 4) {
                        Text("Season \(season)/\(String(season + 1).suffix(2))")
                            .font(.headline)
                            .foregroundColor(.primary)
                        
                        Text(isSynced ? "Already synced" : "Not synced")
                            .font(.caption)
                            .foregroundColor(isSynced ? .green : .orange)
                    }
                    
                    Spacer()
                    
                    if isSynced {
                        Image(systemName: "checkmark.circle.fill")
                            .foregroundColor(.green)
                    }
                }
            }
            .buttonStyle(.plain)
        }
        .padding()
        .background(Color(.systemGray))
        .cornerRadius(8)
        .shadow(radius: 1)
    }
}

#Preview {
    DataSyncView(
        dataSyncService: DataSyncService(coordinator: FootballDataCoordinator()),
        syncStatus: [2024: true, 2023: false, 2022: true]
    )
}
