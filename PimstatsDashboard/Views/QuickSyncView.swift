//
//  QuickSyncView.swift
//  PimstatsDashboard
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 15.06.2025.
//

import SwiftUI

struct QuickSyncView: View {
    @State private var selectedLeague: League = League.premierLeague
    @State private var selectedSeason: Int = 2024
    @State private var isTestingAPI = false
    @State private var testResult: String = ""
    @State private var showingLeagueSync = false
    
    var body: some View {
        NavigationView {
            VStack(spacing: 30) {
                // Header
                VStack(spacing: 16) {
                    Image(systemName: "arrow.clockwise.circle.fill")
                        .font(.system(size: 60))
                        .foregroundColor(.blue)
                    
                    Text("Quick Sync")
                        .font(.largeTitle)
                        .fontWeight(.bold)
                    
                    Text("Test API and sync football data")
                        .font(.title2)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                }
                
                leagueAndSeasonSelectionView
                
                // Test Result
                if !testResult.isEmpty {
                    ScrollView {
                        Text(testResult)
                            .font(.caption)
                            .foregroundColor(.secondary)
                            .padding()
                    }
                    .frame(maxHeight: 150)
                    .background(Color(.systemGray))
                    .cornerRadius(8)
                }
                
                Spacer()
                
                actionButtons
            }
            .padding()
            .navigationTitle("Quick Sync")
            .sheet(isPresented: $showingLeagueSync) {
                LeagueSeasonSyncView()
            }
        }
    }
    
    var leagueAndSeasonSelectionView: some View {
        // League and Season Selection
        VStack(spacing: 20) {
            VStack(alignment: .leading, spacing: 8) {
                Text("League")
                    .font(.headline)
                
                Picker("League", selection: $selectedLeague) {
                    ForEach(League.supportedLeagues, id: \.id) { league in
                        Text("\(league.name) (\(league.country))").tag(league)
                    }
                }
                .pickerStyle(.menu)
            }
            
            VStack(alignment: .leading, spacing: 8) {
                Text("Season")
                    .font(.headline)
                
                Picker("Season", selection: $selectedSeason) {
                    ForEach(FootballAPIConfig.availableSeasons, id: \.self) { season in
                        Text("\(season)/\(String(season + 1).suffix(2))").tag(season)
                    }
                }
                .pickerStyle(.menu)
            }
        }
        .padding()
        .background(Color(.systemGray))
        .cornerRadius(12)
    }
    
    var actionButtons: some View {
        // Action Buttons
        VStack(spacing: 12) {
            Button(action: {
                Task {
                    await testAPI()
                }
            }) {
                if isTestingAPI {
                    HStack {
                        ProgressView()
                            .scaleEffect(0.8)
                        Text("Testing API...")
                    }
                } else {
                    Label("Test API Connection", systemImage: "network")
                }
            }
            .frame(maxWidth: .infinity)
            .buttonStyle(.borderedProminent)
            .disabled(isTestingAPI)
            
            Button(action: {
                showingLeagueSync = true
            }) {
                Label("Open League & Season Sync", systemImage: "arrow.down.circle")
                    .frame(maxWidth: .infinity)
            }
            .buttonStyle(.bordered)
            
            NavigationLink(destination: FootballDashboardView()) {
                Label("Open Dashboard", systemImage: "chart.bar.fill")
                    .frame(maxWidth: .infinity)
            }
            .buttonStyle(.bordered)
        }
        .padding(.horizontal)
    }
    
    private func testAPI() async {
        isTestingAPI = true
        testResult = ""
        
        do {
            testResult += "🔍 Testing API connection for \(selectedLeague.name) \(selectedSeason)...\n"
            
            let apiService = FootballAPIService()
            
            // Test teams endpoint
            testResult += "📥 Fetching teams...\n"
            let teams = try await apiService.fetchTeams(for: selectedSeason, league: selectedLeague)
            testResult += "✅ Found \(teams.count) teams\n"
            
            // Show first few teams
            let firstTeams = teams.prefix(3)
            for team in firstTeams {
                testResult += "  • \(team.name)\n"
            }
            if teams.count > 3 {
                testResult += "  • ... and \(teams.count - 3) more\n"
            }
            
            // Test matches endpoint
            testResult += "\n📥 Fetching matches...\n"
            let matches = try await apiService.fetchMatches(for: selectedSeason, league: selectedLeague)
            testResult += "✅ Found \(matches.count) matches\n"
            
            let finishedMatches = matches.filter { $0.status == "FINISHED" }
            testResult += "  • \(finishedMatches.count) finished matches\n"
            
            let scheduledMatches = matches.filter { $0.status == "SCHEDULED" }
            testResult += "  • \(scheduledMatches.count) scheduled matches\n"
            
            // Process data
            testResult += "\n🔄 Processing data...\n"
            let (stats, teamNames, matchdays, _, _) = apiService.processData(matches, teams)
            testResult += "✅ Processed data for \(teamNames.count) teams across \(matchdays.count) matchdays\n"
            
            testResult += "\n🎉 API test completed successfully!\n"
            testResult += "Ready to sync \(selectedLeague.name) \(selectedSeason) data to Firebase."
            
        } catch {
            testResult += "\n❌ API test failed: \(error.localizedDescription)\n"
            
            if let apiError = error as? FootballAPIError {
                switch apiError {
                case .forbidden:
                    testResult += "💡 Try a different season - this one might not be available.\n"
                case .rateLimitExceeded:
                    testResult += "💡 API rate limit exceeded. Wait a moment and try again.\n"
                default:
                    testResult += "💡 Check your internet connection and try again.\n"
                }
            }
        }
        
        isTestingAPI = false
    }
}

#Preview {
    QuickSyncView()
}
