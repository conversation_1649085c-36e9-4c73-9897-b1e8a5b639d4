//
//  FirebaseModels.swift
//  PimstatsDashboard
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 15.06.2025.
//

import Foundation
import FirebaseFirestore

// MARK: - Team Model for Firebase
struct FirebaseTeam: Codable, Identifiable {
    @DocumentID var id: String?
    let teamId: Int
    let name: String
    let crest: String?
    let clubColors: String?
    let league: String // e.g., "PL"
    let createdAt: Date
    let updatedAt: Date
    
    init(teamId: Int, name: String, crest: String? = nil, clubColors: String? = nil, league: String = "PL") {
        self.teamId = teamId
        self.name = name
        self.crest = crest
        self.clubColors = clubColors
        self.league = league
        self.createdAt = Date()
        self.updatedAt = Date()
    }
    
    // Convert from API TeamDetails
    init(from teamDetails: TeamDetails, league: String = "PL") {
        self.teamId = teamDetails.id
        self.name = teamDetails.name
        self.crest = teamDetails.crest
        self.clubColors = teamDetails.clubColors
        self.league = league
        self.createdAt = Date()
        self.updatedAt = Date()
    }
}

// MARK: - Matchday Performance Model
struct MatchdayPerformance: Codable {
    let matchday: Int
    let points: Int
    let goalsFor: Int
    let goalsAgainst: Int
    let goalDifference: Int
    let position: Int? // League position after this matchday
    let updatedAt: Date
    
    init(matchday: Int, points: Int, goalsFor: Int, goalsAgainst: Int, position: Int? = nil) {
        self.matchday = matchday
        self.points = points
        self.goalsFor = goalsFor
        self.goalsAgainst = goalsAgainst
        self.goalDifference = goalsFor - goalsAgainst
        self.position = position
        self.updatedAt = Date()
    }
}

// MARK: - Team Season Performance Model
struct TeamSeasonPerformance: Codable, Identifiable {
    @DocumentID var id: String?
    let teamId: Int
    let teamName: String
    let season: Int
    let league: String
    let matchdayPerformances: [MatchdayPerformance]
    let totalPoints: Int
    let totalGoalsFor: Int
    let totalGoalsAgainst: Int
    let totalGoalDifference: Int
    let finalPosition: Int?
    let createdAt: Date
    let updatedAt: Date
    
    init(teamId: Int, teamName: String, season: Int, league: String = "PL", 
         matchdayPerformances: [MatchdayPerformance] = [], finalPosition: Int? = nil) {
        self.teamId = teamId
        self.teamName = teamName
        self.season = season
        self.league = league
        self.matchdayPerformances = matchdayPerformances
        self.totalPoints = matchdayPerformances.last?.points ?? 0
        self.totalGoalsFor = matchdayPerformances.last?.goalsFor ?? 0
        self.totalGoalsAgainst = matchdayPerformances.last?.goalsAgainst ?? 0
        self.totalGoalDifference = totalGoalsFor - totalGoalsAgainst
        self.finalPosition = finalPosition
        self.createdAt = Date()
        self.updatedAt = Date()
    }
}

// MARK: - Season Summary Model
struct SeasonSummary: Codable, Identifiable {
    @DocumentID var id: String?
    let season: Int
    let league: String
    let totalMatchdays: Int
    let totalTeams: Int
    let isComplete: Bool
    let lastUpdated: Date
    let createdAt: Date
    
    init(season: Int, league: String = "PL", totalMatchdays: Int = 0, totalTeams: Int = 0, isComplete: Bool = false) {
        self.season = season
        self.league = league
        self.totalMatchdays = totalMatchdays
        self.totalTeams = totalTeams
        self.isComplete = isComplete
        self.lastUpdated = Date()
        self.createdAt = Date()
    }
}

// MARK: - Firebase Collection Names
struct FirebaseCollections {
    static let teams = "teams"
    static let seasons = "seasons"
    static let teamSeasonPerformances = "team_season_performances"
    static let seasonSummaries = "season_summaries"
}

// MARK: - Firebase Document Helpers
extension FirebaseTeam {
    var documentPath: String {
        return "\(FirebaseCollections.teams)/\(league)_\(teamId)"
    }
}

extension TeamSeasonPerformance {
    var documentPath: String {
        return "\(FirebaseCollections.teamSeasonPerformances)/\(league)_\(season)_\(teamId)"
    }
}

extension SeasonSummary {
    var documentPath: String {
        return "\(FirebaseCollections.seasonSummaries)/\(league)_\(season)"
    }
}
