//
//  FootballAPIModels.swift
//  PimstatsDashboard
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 15.06.2025.
//

import Foundation

// MARK: - Team Details from API
struct TeamDetails: Decodable {
    let id: Int
    let name: String
    let crest: String? // Team logo URL
    let clubColors: String? // Team colors (e.g., "Red / White")
}

struct TeamsResponse: Decodable {
    let teams: [TeamDetails]
}

// MARK: - Match Data from API
struct Match: Decodable {
    struct Team: Decodable {
        let id: Int
        let name: String
    }

    struct Score: Decodable {
        struct FullTime: Decodable {
            let home: Int?
            let away: Int?
        }
        let fullTime: FullTime
    }

    let status: String
    let matchday: Int
    let utcDate: String
    let homeTeam: Team
    let awayTeam: Team
    let score: Score
}

struct MatchesResponse: Decodable {
    let matches: [Match]
}

// MARK: - Type Aliases for Processing
typealias TeamStats = [Int: (points: Int, gf: Int, ga: Int)]
typealias LeagueStats = [String: TeamStats]

// MARK: - API Configuration
struct FootballAPIConfig {
    static let token = "0f296f6c02384ad6bb36d034affa7ada"
    static let baseURL = "https://api.football-data.org/v4"
    static let league = "PL"
    static let headers = ["X-Auth-Token": token]
    
    static let availableSeasons = [
        2024, 2023, 2022, 2021, 2020, 2019, 2018, 2017, 2016, 2015
    ]
}

// MARK: - API Error Types
enum FootballAPIError: Error, LocalizedError {
    case invalidURL
    case forbidden(season: Int)
    case rateLimitExceeded
    case badServerResponse(statusCode: Int)
    case decodingError(Error)
    case networkError(Error)
    
    var errorDescription: String? {
        switch self {
        case .invalidURL:
            return "Invalid API URL"
        case .forbidden(let season):
            return "Season \(season) is not available or API limit reached"
        case .rateLimitExceeded:
            return "API rate limit exceeded. Please wait and try again."
        case .badServerResponse(let statusCode):
            return "Server error with status code: \(statusCode)"
        case .decodingError(let error):
            return "Failed to decode API response: \(error.localizedDescription)"
        case .networkError(let error):
            return "Network error: \(error.localizedDescription)"
        }
    }
}

// MARK: - Chart Data Model (for UI)
struct Entry: Identifiable {
    let id: String
    let value: Double
    
    init(id: String, value: Double) {
        self.id = id
        self.value = value
    }
}
