//
//  TeamStyles.swift
//  PimstatsDashboard
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 15.06.2025.
//

import SwiftUI

// MARK: - Team Style Model
struct TeamStyle {
    let primaryColor: Color
    let secondaryColor: Color?
    let pattern: String // "solid", "gradient", "stripe"
}

// MARK: - Team Colors and Styles
let teamStyles: [String: TeamStyle] = [
    // Premier League
    "Arsenal": TeamStyle(primaryColor: .red, secondaryColor: .white, pattern: "gradient"),
    "Manchester City": TeamStyle(primaryColor: Color(red: 0.4, green: 0.8, blue: 1.0), secondaryColor: .white, pattern: "solid"),
    "Liverpool": TeamStyle(primaryColor: .red, secondaryColor: nil, pattern: "solid"),
    "Chelsea": TeamStyle(primaryColor: .blue, secondaryColor: .white, pattern: "solid"),
    "Manchester United": TeamStyle(primaryColor: .red, secondaryColor: .yellow, pattern: "gradient"),
    "Newcastle United": TeamStyle(primaryColor: .black, secondaryColor: .white, pattern: "stripe"),
    "Tottenham Hotspur": TeamStyle(primaryColor: .white, secondaryColor: .blue, pattern: "gradient"),
    "Brighton & Hove Albion": TeamStyle(primaryColor: .blue, secondaryColor: .white, pattern: "stripe"),
    "Aston Villa": TeamStyle(primaryColor: Color(red: 0.5, green: 0.0, blue: 0.5), secondaryColor: .blue, pattern: "solid"),
    "West Ham United": TeamStyle(primaryColor: Color(red: 0.5, green: 0.0, blue: 0.5), secondaryColor: .blue, pattern: "gradient"),
    "Crystal Palace": TeamStyle(primaryColor: .blue, secondaryColor: .red, pattern: "stripe"),
    "Fulham": TeamStyle(primaryColor: .white, secondaryColor: .black, pattern: "solid"),
    "Brentford": TeamStyle(primaryColor: .red, secondaryColor: .white, pattern: "stripe"),
    "Wolverhampton Wanderers": TeamStyle(primaryColor: .orange, secondaryColor: .black, pattern: "gradient"),
    "Everton": TeamStyle(primaryColor: .blue, secondaryColor: .white, pattern: "solid"),
    "Nottingham Forest": TeamStyle(primaryColor: .red, secondaryColor: .white, pattern: "solid"),
    "Luton Town": TeamStyle(primaryColor: .orange, secondaryColor: .blue, pattern: "gradient"),
    "Burnley": TeamStyle(primaryColor: Color(red: 0.5, green: 0.0, blue: 0.5), secondaryColor: .blue, pattern: "solid"),
    "Sheffield United": TeamStyle(primaryColor: .red, secondaryColor: .white, pattern: "stripe"),
    "Bournemouth": TeamStyle(primaryColor: .red, secondaryColor: .black, pattern: "stripe"),
    
    // Default fallback
    "Default": TeamStyle(primaryColor: .gray, secondaryColor: nil, pattern: "solid")
]

// MARK: - Helper Functions
@MainActor func getTeamColor(_ teamName: String, dataManager: FootballDataManager? = nil) -> Color {
    // First try to get color from team colors if available
    if let dataManager = dataManager,
       let clubColors = dataManager.teamColors[teamName] {
        return parseClubColors(clubColors)
    }
    
    // Fallback to predefined styles
    return teamStyles[teamName]?.primaryColor ?? teamStyles["Default"]!.primaryColor
}

func parseClubColors(_ clubColors: String) -> Color {
    let colors = clubColors.lowercased()
    
    if colors.contains("red") {
        return .red
    } else if colors.contains("blue") {
        return .blue
    } else if colors.contains("green") {
        return .green
    } else if colors.contains("yellow") {
        return .yellow
    } else if colors.contains("orange") {
        return .orange
    } else if colors.contains("purple") {
        return .purple
    } else if colors.contains("black") {
        return .black
    } else if colors.contains("white") {
        return .gray // Use gray instead of white for visibility
    } else {
        return .gray
    }
}

func getShortTeamName(_ teamName: String) -> String {
    let shortNames: [String: String] = [
        // Premier League
        "Arsenal": "ARS",
        "Manchester City": "MCI",
        "Liverpool": "LIV",
        "Chelsea": "CHE",
        "Manchester United": "MUN",
        "Newcastle United": "NEW",
        "Tottenham Hotspur": "TOT",
        "Brighton & Hove Albion": "BHA",
        "Aston Villa": "AVL",
        "West Ham United": "WHU",
        "Crystal Palace": "CRY",
        "Fulham": "FUL",
        "Brentford": "BRE",
        "Wolverhampton Wanderers": "WOL",
        "Everton": "EVE",
        "Nottingham Forest": "NFO",
        "Luton Town": "LUT",
        "Burnley": "BUR",
        "Sheffield United": "SHU",
        "Bournemouth": "BOU"
    ]
    
    return shortNames[teamName] ?? String(teamName.prefix(3)).uppercased()
}
