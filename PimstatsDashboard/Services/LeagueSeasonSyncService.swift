//
//  LeagueSeasonSyncService.swift
//  PimstatsDashboard
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 15.06.2025.
//

import Foundation

@MainActor
class LeagueSeasonSyncService: ObservableObject {
    
    // MARK: - Published Properties
    @Published var isLoading = false
    @Published var syncProgress: Double = 0.0
    @Published var syncStatus: String = ""
    @Published var errorMessage: String?
    @Published var lastSyncResult: SyncResult?
    
    // MARK: - Private Properties
    private let apiService = FootballAPIService()
    private let firebaseService = FirebaseService()
    
    // MARK: - Data Models
    
    struct SyncRequest {
        let league: League
        let season: Int
        let forceRefresh: Bool
        
        var identifier: String {
            return "\(league.id)_\(season)"
        }
    }
    
    struct SyncResult {
        let request: SyncRequest
        let success: Bool
        let teamsCount: Int
        let matchdaysCount: Int
        let errorMessage: String?
        let wasAlreadySynced: Bool
        let syncDate: Date
        
        var displayMessage: String {
            if wasAlreadySynced && !request.forceRefresh {
                return "⚠️ \(request.league.name) \(request.season) was already synchronized"
            } else if success {
                return "✅ Successfully synchronized \(request.league.name) \(request.season) - \(teamsCount) teams, \(matchdaysCount) matchdays"
            } else {
                return "❌ Failed to synchronize \(request.league.name) \(request.season): \(errorMessage ?? "Unknown error")"
            }
        }
    }
    
    // MARK: - Public Methods
    
    /// Check if league/season combination is already synchronized
    func isAlreadySynced(league: League, season: Int) async -> Bool {
        do {
            return try await firebaseService.hasDataForSeason(season, league: league.id)
        } catch {
            print("Error checking sync status for \(league.id) \(season): \(error)")
            return false
        }
    }
    
    /// Get sync status for multiple league/season combinations
    func getSyncStatus(for requests: [SyncRequest]) async -> [String: Bool] {
        var status: [String: Bool] = [:]
        
        for request in requests {
            let isSynced = await isAlreadySynced(league: request.league, season: request.season)
            status[request.identifier] = isSynced
        }
        
        return status
    }
    
    /// Sync a specific league and season
    func syncLeagueSeason(league: League, season: Int, forceRefresh: Bool = false) async -> SyncResult {
        let request = SyncRequest(league: league, season: season, forceRefresh: forceRefresh)
        
        isLoading = true
        syncProgress = 0.0
        errorMessage = nil
        syncStatus = "Starting sync for \(league.name) \(season)..."
        
        // Check if already synced
        let alreadySynced = await isAlreadySynced(league: league, season: season)
        
        if alreadySynced && !forceRefresh {
            let result = SyncResult(
                request: request,
                success: true,
                teamsCount: 0,
                matchdaysCount: 0,
                errorMessage: nil,
                wasAlreadySynced: true,
                syncDate: Date()
            )
            
            syncStatus = result.displayMessage
            lastSyncResult = result
            isLoading = false
            
            return result
        }
        
        do {
            // Step 1: Fetch teams (20% progress)
            syncStatus = "Fetching teams for \(league.name) \(season)..."
            let apiTeams = try await apiService.fetchTeams(for: season, league: league)
            syncProgress = 0.2
            
            // Step 2: Fetch matches (40% progress)
            syncStatus = "Fetching matches for \(league.name) \(season)..."
            let apiMatches = try await apiService.fetchMatches(for: season, league: league)
            syncProgress = 0.4
            
            // Step 3: Process data (50% progress)
            syncStatus = "Processing match data..."
            let (leagueStats, teamNames, matchdays, teamLogos, teamColors) = apiService.processData(apiMatches, apiTeams)
            syncProgress = 0.5
            
            // Step 4: Convert to Firebase models (60% progress)
            syncStatus = "Converting to Firebase format..."
            let firebaseTeams = apiTeams.map { FirebaseTeam(from: $0, league: league.id) }
            let teamSeasonPerformances = convertToTeamSeasonPerformances(
                leagueStats: leagueStats,
                teams: apiTeams,
                season: season,
                league: league.id,
                matchdays: matchdays
            )
            syncProgress = 0.6
            
            // Step 5: Save teams (avoiding duplicates) (70% progress)
            syncStatus = "Saving teams to Firebase..."
            try await saveTeamsWithoutDuplicates(firebaseTeams)
            syncProgress = 0.7
            
            // Step 6: Save season performances (90% progress)
            syncStatus = "Saving season performances..."
            try await firebaseService.saveTeamSeasonPerformances(teamSeasonPerformances)
            syncProgress = 0.9
            
            // Step 7: Save season summary (100% progress)
            syncStatus = "Saving season summary..."
            let seasonSummary = SeasonSummary(
                season: season,
                league: league.id,
                totalMatchdays: matchdays.count,
                totalTeams: teamNames.count,
                isComplete: isSeasonComplete(matchdays: matchdays, totalTeams: teamNames.count)
            )
            try await firebaseService.saveSeasonSummary(seasonSummary)
            syncProgress = 1.0
            
            let result = SyncResult(
                request: request,
                success: true,
                teamsCount: teamNames.count,
                matchdaysCount: matchdays.count,
                errorMessage: nil,
                wasAlreadySynced: false,
                syncDate: Date()
            )
            
            syncStatus = result.displayMessage
            lastSyncResult = result
            
            print("✅ Successfully synced \(league.name) \(season)")
            
            isLoading = false
            return result
            
        } catch {
            let result = SyncResult(
                request: request,
                success: false,
                teamsCount: 0,
                matchdaysCount: 0,
                errorMessage: error.localizedDescription,
                wasAlreadySynced: false,
                syncDate: Date()
            )
            
            errorMessage = error.localizedDescription
            syncStatus = result.displayMessage
            lastSyncResult = result
            
            print("❌ Failed to sync \(league.name) \(season): \(error)")
            
            isLoading = false
            return result
        }
    }
    
    /// Sync multiple league/season combinations
    func syncMultiple(_ requests: [SyncRequest]) async -> [SyncResult] {
        var results: [SyncResult] = []
        
        for (index, request) in requests.enumerated() {
            syncStatus = "Syncing \(request.league.name) \(request.season) (\(index + 1)/\(requests.count))..."
            
            let result = await syncLeagueSeason(
                league: request.league,
                season: request.season,
                forceRefresh: request.forceRefresh
            )
            
            results.append(result)
            
            // Add delay between syncs to respect API rate limits
            if index < requests.count - 1 {
                syncStatus = "Waiting before next sync..."
                try? await Task.sleep(nanoseconds: 2_000_000_000) // 2 seconds
            }
        }
        
        syncStatus = "✅ Completed syncing \(requests.count) league/season combinations"
        return results
    }
    
    /// Get available seasons for a specific league
    func getAvailableSeasons(for league: League) async -> [Int] {
        return await apiService.checkAvailableSeasons(for: league)
    }
    
    // MARK: - Private Methods
    
    private func saveTeamsWithoutDuplicates(_ teams: [FirebaseTeam]) async throws {
        // Fetch existing teams to avoid duplicates
        let existingTeams = try await firebaseService.fetchTeams(for: teams.first?.league ?? "PL")
        let existingTeamIds = Set(existingTeams.map { $0.teamId })
        
        // Filter out teams that already exist
        let newTeams = teams.filter { !existingTeamIds.contains($0.teamId) }
        
        if !newTeams.isEmpty {
            try await firebaseService.saveTeams(newTeams)
            print("✅ Saved \(newTeams.count) new teams (skipped \(teams.count - newTeams.count) duplicates)")
        } else {
            print("ℹ️ All teams already exist, no new teams to save")
        }
    }
    
    private func convertToTeamSeasonPerformances(
        leagueStats: LeagueStats,
        teams: [TeamDetails],
        season: Int,
        league: String,
        matchdays: [Int]
    ) -> [TeamSeasonPerformance] {
        
        var performances: [TeamSeasonPerformance] = []
        
        // Create team ID to name mapping
        let teamIdToName = Dictionary(uniqueKeysWithValues: teams.map { ($0.id, $0.name) })
        let teamNameToId = Dictionary(uniqueKeysWithValues: teams.map { ($0.name, $0.id) })
        
        for (teamName, teamStats) in leagueStats {
            guard let teamId = teamNameToId[teamName] else { continue }
            
            // Convert team stats to matchday performances
            let matchdayPerformances = matchdays.compactMap { matchday -> MatchdayPerformance? in
                guard let stats = teamStats[matchday] else { return nil }
                return MatchdayPerformance(
                    matchday: matchday,
                    points: stats.points,
                    goalsFor: stats.gf,
                    goalsAgainst: stats.ga
                )
            }
            
            let performance = TeamSeasonPerformance(
                teamId: teamId,
                teamName: teamName,
                season: season,
                league: league,
                matchdayPerformances: matchdayPerformances
            )
            
            performances.append(performance)
        }
        
        return performances
    }
    
    private func isSeasonComplete(matchdays: [Int], totalTeams: Int) -> Bool {
        // A complete season typically has (totalTeams - 1) * 2 matchdays
        let expectedMatchdays = (totalTeams - 1) * 2
        return matchdays.count >= expectedMatchdays
    }
}
