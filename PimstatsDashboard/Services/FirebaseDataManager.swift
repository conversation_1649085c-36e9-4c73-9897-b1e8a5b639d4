//
//  FirebaseDataManager.swift
//  PimstatsDashboard
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 15.06.2025.
//

import Foundation

/// Enhanced data manager that works with Firebase data for the bar race view
@MainActor
class FirebaseDataManager: ObservableObject {
    @Published var leagueStats: LeagueStats = [:]
    @Published var teams: [String] = []
    @Published var matchdays: [Int] = []
    @Published var teamLogos: [String: String] = [:] // Team name -> Logo URL
    @Published var teamColors: [String: String] = [:] // Team name -> Club colors
    @Published var selectedSeason: Int = 2024
    @Published var selectedLeague: League = League.premierLeague
    @Published var isLoading = false
    @Published var errorMessage: String?
    
    private let firebaseService = FirebaseService()
    private var seasonPerformances: [TeamSeasonPerformance] = []
    
    func loadData() async {
        await loadData(for: selectedSeason, league: selectedLeague)
    }

    func loadData(for season: Int, league: League) async {
        isLoading = true
        errorMessage = nil
        selectedSeason = season
        selectedLeague = league

        // Clear existing data when changing seasons/leagues
        leagueStats = [:]
        teams = []
        matchdays = []
        teamLogos = [:]
        teamColors = [:]

        do {
            // Fetch team season performances from Firebase
            seasonPerformances = try await firebaseService.fetchTeamSeasonPerformances(for: season, league: league.id)
            
            if seasonPerformances.isEmpty {
                errorMessage = "No data found for \(league.name) \(season). Please sync this league/season first."
                isLoading = false
                return
            }
            
            // Fetch teams for logos and colors
            let firebaseTeams = try await firebaseService.fetchTeams(for: league.id)
            
            // Process the data
            processFirebaseData(seasonPerformances, firebaseTeams)
            
            print("Loaded Firebase data for \(league.name) \(season): \(teams.count) teams, \(matchdays.count) matchdays")
            
        } catch {
            print("Firebase data loading failed for \(league.name) \(season), error: \(error)")
            errorMessage = "Failed to load data: \(error.localizedDescription)"
        }

        isLoading = false
    }
    
    private func processFirebaseData(_ performances: [TeamSeasonPerformance], _ firebaseTeams: [FirebaseTeam]) {
        var stats: LeagueStats = [:]
        var teamNames: Set<String> = []
        var matchdaySet: Set<Int> = []
        var logos: [String: String] = [:]
        var colors: [String: String] = [:]
        
        // Process team logos and colors
        for team in firebaseTeams {
            if let crest = team.crest {
                logos[team.name] = crest
            }
            if let clubColors = team.clubColors {
                colors[team.name] = clubColors
            }
        }
        
        // Process season performances
        for performance in performances {
            teamNames.insert(performance.teamName)
            stats[performance.teamName] = [:]
            
            // Convert matchday performances to the expected format
            for matchdayPerf in performance.matchdayPerformances {
                matchdaySet.insert(matchdayPerf.matchday)
                stats[performance.teamName]![matchdayPerf.matchday] = (
                    points: matchdayPerf.points,
                    gf: matchdayPerf.goalsFor,
                    ga: matchdayPerf.goalsAgainst
                )
            }
        }
        
        // Update published properties
        self.leagueStats = stats
        self.teams = Array(teamNames).sorted()
        self.matchdays = Array(matchdaySet).sorted()
        self.teamLogos = logos
        self.teamColors = colors
    }

    func getPointsForMatchday(_ matchday: Int) -> [Entry] {
        var entries: [Entry] = []

        for team in teams {
            let points = leagueStats[team]?[matchday]?.points ?? 0
            entries.append(Entry(id: team, value: Double(points)))
        }

        return entries.sorted { $0.value > $1.value }
    }
    
    /// Get available leagues that have data
    func getAvailableLeagues() async -> [League] {
        var availableLeagues: [League] = []
        
        for league in League.supportedLeagues {
            do {
                let teams = try await firebaseService.fetchTeams(for: league.id)
                if !teams.isEmpty {
                    availableLeagues.append(league)
                }
            } catch {
                // League has no data, skip
                continue
            }
        }
        
        return availableLeagues
    }
    
    /// Get available seasons for the current league
    func getAvailableSeasons() async -> [Int] {
        do {
            let summaries = try await firebaseService.fetchSeasonSummaries(for: selectedLeague.id)
            return summaries.map { $0.season }.sorted(by: >)
        } catch {
            return []
        }
    }
    
    /// Check if data exists for a specific league/season combination
    func hasData(for season: Int, league: League) async -> Bool {
        do {
            let performances = try await firebaseService.fetchTeamSeasonPerformances(for: season, league: league.id)
            return !performances.isEmpty
        } catch {
            return false
        }
    }
}
