//
//  GitHubFootballService.swift
//  PimstatsDashboard
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 15.06.2025.
//

import Foundation

@MainActor
class GitHubFootballService: ObservableObject {
    
    // MARK: - Published Properties
    @Published var isLoading = false
    @Published var errorMessage: String?
    
    // MARK: - Private Properties
    private let session: URLSession
    private let baseURL = "https://raw.githubusercontent.com/openfootball/football.json/master"
    
    init() {
        // Configure URLSession with timeout
        let config = URLSessionConfiguration.default
        config.timeoutIntervalForRequest = 30
        config.timeoutIntervalForResource = 60
        self.session = URLSession(configuration: config)
    }
    
    // MARK: - Public Methods
    
    /// Fetch teams for a specific season and league
    func fetchTeams(for season: Int, league: League = FootballAPIConfig.defaultLeague) async throws -> [TeamDetails] {
        // Map league to GitHub repository format
        let leagueCode = mapLeagueToGitHubFormat(league)
        let seasonStr = formatSeasonString(season)
        
        guard let url = URL(string: "\(baseURL)/\(seasonStr)/\(leagueCode).clubs.json") else {
            throw FootballAPIError.invalidURL
        }
        
        do {
            let (data, response) = try await session.data(from: url)
            
            // Check HTTP response
            if let httpResponse = response as? HTTPURLResponse {
                print("GitHub Teams HTTP Status: \(httpResponse.statusCode)")
                if httpResponse.statusCode != 200 {
                    throw FootballAPIError.badServerResponse(statusCode: httpResponse.statusCode)
                }
            }
            
            // Debug: Print raw response
            if let jsonString = String(data: data, encoding: .utf8) {
                print("GitHub Teams Response (first 500 chars): \(String(jsonString.prefix(500)))")
            }
            
            do {
                // Parse GitHub JSON format
                let decoded = try JSONDecoder().decode(GitHubClubsResponse.self, from: data)
                print("Successfully decoded \(decoded.clubs.count) teams from GitHub")
                
                // Convert to our app's TeamDetails format
                return decoded.clubs.map { club in
                    TeamDetails(
                        id: club.code.hashValue, // Generate ID from code
                        name: club.name,
                        crest: club.country.contains("England") ? 
                              "https://crests.football-data.org/\(club.code).png" : nil,
                        clubColors: nil // GitHub data doesn't include colors
                    )
                }
            } catch {
                print("GitHub Teams decoding error: \(error)")
                throw FootballAPIError.decodingError(error)
            }
        } catch let error as FootballAPIError {
            throw error
        } catch {
            throw FootballAPIError.networkError(error)
        }
    }
    
    /// Fetch matches for a specific season and league
    func fetchMatches(for season: Int, league: League = FootballAPIConfig.defaultLeague) async throws -> [Match] {
        // Map league to GitHub repository format
        let leagueCode = mapLeagueToGitHubFormat(league)
        let seasonStr = formatSeasonString(season)
        
        guard let url = URL(string: "\(baseURL)/\(seasonStr)/\(leagueCode).json") else {
            throw FootballAPIError.invalidURL
        }
        
        do {
            let (data, response) = try await session.data(from: url)
            
            // Check HTTP response
            if let httpResponse = response as? HTTPURLResponse {
                print("GitHub Matches HTTP Status: \(httpResponse.statusCode)")
                if httpResponse.statusCode != 200 {
                    throw FootballAPIError.badServerResponse(statusCode: httpResponse.statusCode)
                }
            }
            
            // Debug: Print raw response
            if let jsonString = String(data: data, encoding: .utf8) {
                print("GitHub Matches Response (first 500 chars): \(String(jsonString.prefix(500)))")
            }
            
            do {
                // Parse GitHub JSON format
                let decoded = try JSONDecoder().decode(GitHubLeagueResponse.self, from: data)
                print("Successfully decoded GitHub matches data with \(decoded.matches.count) matches")
                
                // Convert to our app's Match format
                return decoded.matches.compactMap { match in
                    guard let homeScore = match.score.ft.first,
                          let awayScore = match.score.ft.last,
                          let matchday = match.round?.replacingOccurrences(of: "Matchday ", with: "").toInt() else {
                        return nil
                    }
                    
                    return Match(
                        status: "FINISHED",
                        matchday: matchday,
                        utcDate: match.date,
                        homeTeam: Match.Team(id: match.team1.code.hashValue, name: match.team1.name),
                        awayTeam: Match.Team(id: match.team2.code.hashValue, name: match.team2.name),
                        score: Match.Score(fullTime: Match.Score.FullTime(home: homeScore, away: awayScore))
                    )
                }
            } catch {
                print("GitHub Matches decoding error: \(error)")
                throw FootballAPIError.decodingError(error)
            }
        } catch let error as FootballAPIError {
            throw error
        } catch {
            throw FootballAPIError.networkError(error)
        }
    }
    
    /// Check which seasons are available for a specific league
    func checkAvailableSeasons(for league: League = FootballAPIConfig.defaultLeague) async -> [Int] {
        print("🔍 Checking available seasons from GitHub for \(league.name)...")
        var availableSeasons: [Int] = []
        
        // GitHub repository typically has data for these seasons
        let potentialSeasons = [2020, 2021, 2022, 2023, 2024]
        let leagueCode = mapLeagueToGitHubFormat(league)
        
        for testSeason in potentialSeasons {
            let seasonStr = formatSeasonString(testSeason)
            guard let url = URL(string: "\(baseURL)/\(seasonStr)/\(leagueCode).clubs.json") else {
                continue
            }
            
            do {
                let (_, response) = try await session.data(from: url)
                if let httpResponse = response as? HTTPURLResponse {
                    if httpResponse.statusCode == 200 {
                        print("✅ GitHub Season \(testSeason): Available")
                        availableSeasons.append(testSeason)
                    } else {
                        print("❌ GitHub Season \(testSeason): HTTP \(httpResponse.statusCode)")
                    }
                }
            } catch {
                print("❌ GitHub Season \(testSeason): \(error)")
            }
        }
        
        return availableSeasons.sorted(by: >)
    }
    
    // MARK: - Private Methods
    
    private func mapLeagueToGitHubFormat(_ league: League) -> String {
        switch league.id {
            case "PL": return "en.1"
            case "PD": return "es.1"
            case "BL1": return "de.1"
            case "SA": return "it.1"
            case "FL1": return "fr.1"
            case "DED": return "nl.1"
            case "PPL": return "pt.1"
            case "ELC": return "en.2"
            default: return "en.1" // Default to Premier League
        }
    }
    
    private func formatSeasonString(_ season: Int) -> String {
        return "\(season)-\(season + 1)"
    }
}

// MARK: - GitHub API Models
struct GitHubClubsResponse: Decodable {
    let name: String
    let clubs: [GitHubClub]
}

struct GitHubClub: Decodable {
    let name: String
    let code: String
    let country: String
}

struct GitHubLeagueResponse: Decodable {
    let name: String
    let matches: [GitHubMatch]
}

struct GitHubMatch: Decodable {
    let round: String?
    let date: String
    let team1: GitHubTeam
    let team2: GitHubTeam
    let score: GitHubScore
}

struct GitHubTeam: Decodable {
    let name: String
    let code: String
}

struct GitHubScore: Decodable {
    let ft: [Int]
}

// Helper extension
extension String {
    func toInt() -> Int? {
        return Int(self)
    }
}