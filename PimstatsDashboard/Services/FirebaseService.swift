//
//  FirebaseService.swift
//  PimstatsDashboard
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 15.06.2025.
//

import Foundation
import FirebaseFirestore

@MainActor
class FirebaseService: ObservableObject {
    
    // MARK: - Properties
    private let db = Firestore.firestore()
    @Published var isLoading = false
    @Published var errorMessage: String?
    
    // MARK: - Team Operations
    
    /// Save or update a team in Firebase
    func saveTeam(_ team: FirebaseTeam) async throws {
        let documentPath = team.documentPath
        
        do {
            try await db.document(documentPath).setData(from: team, merge: true)
            print("✅ Team saved: \(team.name)")
        } catch {
            print("❌ Error saving team \(team.name): \(error)")
            throw error
        }
    }
    
    /// Save multiple teams in a batch
    func saveTeams(_ teams: [FirebaseTeam]) async throws {
        let batch = db.batch()
        
        for team in teams {
            let documentRef = db.document(team.documentPath)
            do {
                try batch.setData(from: team, forDocument: documentRef, merge: true)
            } catch {
                print("❌ Error preparing team \(team.name) for batch: \(error)")
                throw error
            }
        }
        
        do {
            try await batch.commit()
            print("✅ Batch saved \(teams.count) teams")
        } catch {
            print("❌ Error committing team batch: \(error)")
            throw error
        }
    }
    
    /// Fetch all teams for a specific league
    func fetchTeams(for league: String = "PL") async throws -> [FirebaseTeam] {
        do {
            let snapshot = try await db.collection(FirebaseCollections.teams)
                .whereField("league", isEqualTo: league)
                .getDocuments()
            
            let teams = try snapshot.documents.compactMap { document in
                try document.data(as: FirebaseTeam.self)
            }
            
            print("✅ Fetched \(teams.count) teams for league \(league)")
            return teams
        } catch {
            print("❌ Error fetching teams: \(error)")
            throw error
        }
    }
    
    // MARK: - Season Performance Operations
    
    /// Save team season performance
    func saveTeamSeasonPerformance(_ performance: TeamSeasonPerformance) async throws {
        let documentPath = performance.documentPath
        
        do {
            try await db.document(documentPath).setData(from: performance, merge: true)
            print("✅ Team season performance saved: \(performance.teamName) - \(performance.season)")
        } catch {
            print("❌ Error saving team season performance: \(error)")
            throw error
        }
    }
    
    /// Save multiple team season performances in a batch
    func saveTeamSeasonPerformances(_ performances: [TeamSeasonPerformance]) async throws {
        let batch = db.batch()
        
        for performance in performances {
            let documentRef = db.document(performance.documentPath)
            do {
                try batch.setData(from: performance, forDocument: documentRef, merge: true)
            } catch {
                print("❌ Error preparing performance for \(performance.teamName): \(error)")
                throw error
            }
        }
        
        do {
            try await batch.commit()
            print("✅ Batch saved \(performances.count) team season performances")
        } catch {
            print("❌ Error committing performance batch: \(error)")
            throw error
        }
    }
    
    /// Fetch team season performances for a specific season and league
    func fetchTeamSeasonPerformances(for season: Int, league: String = "PL") async throws -> [TeamSeasonPerformance] {
        do {
            let snapshot = try await db.collection(FirebaseCollections.teamSeasonPerformances)
                .whereField("season", isEqualTo: season)
                .whereField("league", isEqualTo: league)
                .getDocuments()
            
            let performances = try snapshot.documents.compactMap { document in
                try document.data(as: TeamSeasonPerformance.self)
            }
            
            print("✅ Fetched \(performances.count) team performances for season \(season)")
            return performances
        } catch {
            print("❌ Error fetching team season performances: \(error)")
            throw error
        }
    }
    
    /// Fetch specific team's season performance
    func fetchTeamSeasonPerformance(teamId: Int, season: Int, league: String = "PL") async throws -> TeamSeasonPerformance? {
        let documentPath = "\(FirebaseCollections.teamSeasonPerformances)/\(league)_\(season)_\(teamId)"
        
        do {
            let document = try await db.document(documentPath).getDocument()
            
            if document.exists {
                let performance = try document.data(as: TeamSeasonPerformance.self)
                print("✅ Fetched performance for team \(teamId) in season \(season)")
                return performance
            } else {
                print("ℹ️ No performance found for team \(teamId) in season \(season)")
                return nil
            }
        } catch {
            print("❌ Error fetching team season performance: \(error)")
            throw error
        }
    }
    
    // MARK: - Season Summary Operations
    
    /// Save season summary
    func saveSeasonSummary(_ summary: SeasonSummary) async throws {
        let documentPath = summary.documentPath
        
        do {
            try await db.document(documentPath).setData(from: summary, merge: true)
            print("✅ Season summary saved: \(summary.league) \(summary.season)")
        } catch {
            print("❌ Error saving season summary: \(error)")
            throw error
        }
    }
    
    /// Fetch all season summaries for a league
    func fetchSeasonSummaries(for league: String = "PL") async throws -> [SeasonSummary] {
        do {
            let snapshot = try await db.collection(FirebaseCollections.seasonSummaries)
                .whereField("league", isEqualTo: league)
                .order(by: "season", descending: true)
                .getDocuments()
            
            let summaries = try snapshot.documents.compactMap { document in
                try document.data(as: SeasonSummary.self)
            }
            
            print("✅ Fetched \(summaries.count) season summaries for league \(league)")
            return summaries
        } catch {
            print("❌ Error fetching season summaries: \(error)")
            throw error
        }
    }
    
    // MARK: - Utility Methods
    
    /// Check if data exists for a specific season
    func hasDataForSeason(_ season: Int, league: String = "PL") async throws -> Bool {
        do {
            let snapshot = try await db.collection(FirebaseCollections.teamSeasonPerformances)
                .whereField("season", isEqualTo: season)
                .whereField("league", isEqualTo: league)
                .limit(to: 1)
                .getDocuments()
            
            return !snapshot.documents.isEmpty
        } catch {
            print("❌ Error checking data for season \(season): \(error)")
            throw error
        }
    }
    
    /// Delete all data for a specific season (useful for re-importing)
    func deleteSeasonData(_ season: Int, league: String = "PL") async throws {
        let batch = db.batch()
        
        // Delete team season performances
        let performancesSnapshot = try await db.collection(FirebaseCollections.teamSeasonPerformances)
            .whereField("season", isEqualTo: season)
            .whereField("league", isEqualTo: league)
            .getDocuments()
        
        for document in performancesSnapshot.documents {
            batch.deleteDocument(document.reference)
        }
        
        // Delete season summary
        let summaryDocumentPath = "\(FirebaseCollections.seasonSummaries)/\(league)_\(season)"
        batch.deleteDocument(db.document(summaryDocumentPath))
        
        try await batch.commit()
        print("✅ Deleted all data for season \(season)")
    }
}
