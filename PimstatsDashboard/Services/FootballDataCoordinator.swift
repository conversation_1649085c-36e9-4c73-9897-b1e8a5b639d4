//
//  FootballDataCoordinator.swift
//  PimstatsDashboard
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 15.06.2025.
//

import Foundation

// Add a new enum for data source selection
enum FootballDataSource {
    case footballDataAPI
    case githubRepository
}

/// Main coordinator that manages the flow between API, Firebase, and UI
@MainActor
class FootballDataCoordinator: ObservableObject {
    
    // MARK: - Published Properties
    @Published var isInitialized = false
    @Published var availableSeasons: [Int] = []
    @Published var syncedSeasons: [Int] = []
    @Published var currentSeason: Int = 2024
    @Published var errorMessage: String?

    // Add data source selection
    @Published var dataSource: FootballDataSource = .footballDataAPI

    // Sync status properties (delegated to DataSyncService)
    var isLoading: Bool {
        dataSyncService.isLoading
    }

    var syncProgress: Double {
        dataSyncService.syncProgress
    }

    var syncStatus: String {
        dataSyncService.syncStatus
    }
    
    // MARK: - Services
    private let apiService = FootballAPIService()
    private let firebaseService = FirebaseService()
    private let githubService = GitHubFootballService()
    private lazy var dataSyncService = DataSyncService(coordinator: self)
    
    // MARK: - Initialization
    
    func initialize() async {
        guard !isInitialized else { return }
        
        do {
            // Check available seasons based on selected data source
            if dataSource == .footballDataAPI {
                availableSeasons = await apiService.checkAvailableSeasons()
            } else {
                availableSeasons = await githubService.checkAvailableSeasons()
            }
            
            // Check which seasons are already synced
            let syncStatus = await dataSyncService.checkSyncStatus()
            syncedSeasons = syncStatus.compactMap { key, value in
                value ? key : nil
            }.sorted(by: >)
            
            // Set current season to the latest available
            if let latestSeason = availableSeasons.first {
                currentSeason = latestSeason
            }
            
            isInitialized = true
            print("✅ FootballDataCoordinator initialized")
            print("Available seasons: \(availableSeasons)")
            print("Synced seasons: \(syncedSeasons)")
            
        } catch {
            errorMessage = "Failed to initialize: \(error.localizedDescription)"
            print("❌ Initialization failed: \(error)")
        }
    }
    
    // MARK: - Data Management
    
    /// Quick sync for the current season
    func quickSyncCurrentSeason() async {
        do {
            try await dataSyncService.syncSeasonData(for: currentSeason)
            await updateSyncedSeasons()
        } catch {
            errorMessage = "Failed to sync current season: \(error.localizedDescription)"
        }
    }
    
    /// Sync multiple seasons
    func syncSeasons(_ seasons: [Int], forceRefresh: Bool = false) async {
        await dataSyncService.syncMultipleSeasons(seasons, forceRefresh: forceRefresh)
        await updateSyncedSeasons()
    }
    
    /// Check if a season has data
    func hasDataForSeason(_ season: Int) async -> Bool {
        do {
            return try await firebaseService.hasDataForSeason(season)
        } catch {
            print("Error checking data for season \(season): \(error)")
            return false
        }
    }
    
    /// Get teams for display
    func getTeams() async -> [FirebaseTeam] {
        do {
            return try await firebaseService.fetchTeams()
        } catch {
            print("Error fetching teams: \(error)")
            return []
        }
    }
    
    /// Get season performances
    func getSeasonPerformances(for season: Int) async -> [TeamSeasonPerformance] {
        do {
            return try await firebaseService.fetchTeamSeasonPerformances(for: season)
        } catch {
            print("Error fetching season performances: \(error)")
            return []
        }
    }
    
    /// Get season summary
    func getSeasonSummary(for season: Int) async -> SeasonSummary? {
        do {
            let summaries = try await firebaseService.fetchSeasonSummaries()
            return summaries.first { $0.season == season }
        } catch {
            print("Error fetching season summary: \(error)")
            return nil
        }
    }
    
    // MARK: - Utility Methods
    
    /// Test API connectivity
    func testAPIConnectivity() async -> Bool {
        do {
            if dataSource == .footballDataAPI {
                let teams = try await apiService.fetchTeams(for: currentSeason)
                print("✅ Football-data.org API connectivity test passed: \(teams.count) teams found")
            } else {
                let teams = try await githubService.fetchTeams(for: currentSeason)
                print("✅ GitHub repository API connectivity test passed: \(teams.count) teams found")
            }
            return true
        } catch {
            print("❌ API connectivity test failed: \(error)")
            errorMessage = "API connectivity test failed: \(error.localizedDescription)"
            return false
        }
    }
    
    /// Test Firebase connectivity
    func testFirebaseConnectivity() async -> Bool {
        do {
            let teams = try await firebaseService.fetchTeams()
            print("✅ Firebase connectivity test passed: \(teams.count) teams found")
            return true
        } catch {
            print("❌ Firebase connectivity test failed: \(error)")
            errorMessage = "Firebase connectivity test failed: \(error.localizedDescription)"
            return false
        }
    }
    
    /// Get sync recommendations
    func getSyncRecommendations() -> [Int] {
        let currentYear = Calendar.current.component(.year, from: Date())
        let recommendedSeasons = [currentYear, currentYear - 1, currentYear - 2]

        return recommendedSeasons.filter { season in
            availableSeasons.contains(season) && !syncedSeasons.contains(season)
        }
    }

    /// Check sync status for all seasons
    func checkSyncStatus() async -> [Int: Bool] {
        return await dataSyncService.checkSyncStatus()
    }

    /// Get the data sync service for UI components
    func getDataSyncService() -> DataSyncService {
        return dataSyncService
    }
    
    // MARK: - Private Methods
    
    private func updateSyncedSeasons() async {
        let syncStatus = await dataSyncService.checkSyncStatus()
        syncedSeasons = syncStatus.compactMap { key, value in
            value ? key : nil
        }.sorted(by: >)
    }
    
    // Add method to switch data sources
    func switchDataSource(to source: FootballDataSource) async {
        dataSource = source
        // Reset and reinitialize with new source
        isInitialized = false
        await initialize()
    }
}

// MARK: - Debug and Testing Extensions
extension FootballDataCoordinator {
    
    /// Run comprehensive system test
    func runSystemTest() async -> Bool {
        print("🧪 Running system test...")
        
        // Test 1: API Connectivity
        guard await testAPIConnectivity() else {
            print("❌ System test failed: API connectivity")
            return false
        }
        
        // Test 2: Firebase Connectivity
        guard await testFirebaseConnectivity() else {
            print("❌ System test failed: Firebase connectivity")
            return false
        }
        
        // Test 3: Data Processing
        do {
            let teams = try await apiService.fetchTeams(for: currentSeason)
            let matches = try await apiService.fetchMatches(for: currentSeason)
            let (stats, teamNames, matchdays, _, _) = apiService.processData(matches, teams)
            
            guard !teamNames.isEmpty && !matchdays.isEmpty else {
                print("❌ System test failed: Data processing returned empty results")
                return false
            }
            
            print("✅ Data processing test passed: \(teamNames.count) teams, \(matchdays.count) matchdays")
        } catch {
            print("❌ System test failed: Data processing error: \(error)")
            return false
        }
        
        print("✅ System test completed successfully")
        return true
    }
    
    /// Get system status
    func getSystemStatus() async -> [String: Any] {
        return [
            "initialized": isInitialized,
            "availableSeasons": availableSeasons,
            "syncedSeasons": syncedSeasons,
            "currentSeason": currentSeason,
            "hasError": errorMessage != nil,
            "errorMessage": errorMessage ?? "",
            "apiConnectivity": await testAPIConnectivity(),
            "firebaseConnectivity": await testFirebaseConnectivity()
        ]
    }
}
