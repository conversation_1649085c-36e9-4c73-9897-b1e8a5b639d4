//
//  FootballAPIService.swift
//  PimstatsDashboard
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 15.06.2025.
//

import Foundation

@MainActor
class FootballAPIService: ObservableObject {

    // MARK: - Published Properties
    @Published var isLoading = false
    @Published var errorMessage: String?

    // MARK: - Private Properties
    private let session: URLSession

    init() {
        // Configure URLSession with timeout
        let config = URLSessionConfiguration.default
        config.timeoutIntervalForRequest = 30
        config.timeoutIntervalForResource = 60
        self.session = URLSession(configuration: config)
    }
    
    // MARK: - Public Methods
    
    /// Fetch teams for a specific season and league
    func fetchTeams(for season: Int, league: League = FootballAPIConfig.defaultLeague) async throws -> [TeamDetails] {
        guard let url = URL(string: "\(FootballAPIConfig.baseURL)/competitions/\(league.id)/teams?season=\(season)") else {
            throw FootballAPIError.invalidURL
        }

        var request = URLRequest(url: url)
        FootballAPIConfig.headers.forEach { key, value in 
            request.setValue(value, forHTTPHeaderField: key) 
        }

        do {
            let (data, response) = try await session.data(for: request)
            
            // Check HTTP response
            if let httpResponse = response as? HTTPURLResponse {
                print("Teams HTTP Status: \(httpResponse.statusCode)")
                try handleHTTPResponse(httpResponse, season: season)
            }

            // Debug: Print raw response
            if let jsonString = String(data: data, encoding: .utf8) {
                print("Teams API Response (first 500 chars): \(String(jsonString.prefix(500)))")
            }

            do {
                let decoded = try JSONDecoder().decode(TeamsResponse.self, from: data)
                print("Successfully decoded \(decoded.teams.count) teams")
                return decoded.teams
            } catch {
                print("Teams decoding error: \(error)")
                throw FootballAPIError.decodingError(error)
            }
        } catch let error as FootballAPIError {
            throw error
        } catch {
            throw FootballAPIError.networkError(error)
        }
    }
    
    /// Fetch matches for a specific season and league
    func fetchMatches(for season: Int, league: League = FootballAPIConfig.defaultLeague) async throws -> [Match] {
        guard let url = URL(string: "\(FootballAPIConfig.baseURL)/competitions/\(league.id)/matches?season=\(season)") else {
            throw FootballAPIError.invalidURL
        }

        var request = URLRequest(url: url)
        FootballAPIConfig.headers.forEach { key, value in 
            request.setValue(value, forHTTPHeaderField: key) 
        }

        do {
            let (data, response) = try await session.data(for: request)

            // Check HTTP response
            if let httpResponse = response as? HTTPURLResponse {
                print("Matches HTTP Status: \(httpResponse.statusCode)")
                try handleHTTPResponse(httpResponse, season: season)
            }

            // Debug: Print raw response
            if let jsonString = String(data: data, encoding: .utf8) {
                print("Raw API Response (first 500 chars): \(String(jsonString.prefix(500)))")
            }

            do {
                let decoded = try JSONDecoder().decode(MatchesResponse.self, from: data)
                print("Successfully decoded \(decoded.matches.count) matches")
                return decoded.matches
            } catch {
                print("Decoding error: \(error)")
                // Try alternative decoding structure
                do {
                    let alternativeDecoded = try JSONDecoder().decode([String: [Match]].self, from: data)
                    print("Successfully decoded with alternative structure")
                    return alternativeDecoded["matches"] ?? []
                } catch {
                    print("Alternative decoding also failed: \(error)")
                    throw FootballAPIError.decodingError(error)
                }
            }
        } catch let error as FootballAPIError {
            throw error
        } catch {
            throw FootballAPIError.networkError(error)
        }
    }
    
    /// Process matches and teams data into league statistics
    func processData(_ matches: [Match], _ teams: [TeamDetails]) -> (LeagueStats, [String], [Int], [String: String], [String: String]) {
        var stats: LeagueStats = [:]
        var standings: [String: (points: Int, gf: Int, ga: Int)] = [:]
        var teamNames: Set<String> = []
        var matchdays: Set<Int> = []
        var teamLogos: [String: String] = [:]
        var teamClubColors: [String: String] = [:]

        // First, build team lookup maps from teams data
        var teamIdToName: [Int: String] = [:]
        for team in teams {
            teamIdToName[team.id] = team.name
            teamNames.insert(team.name)
            if let crest = team.crest {
                teamLogos[team.name] = crest
            }
            if let colors = team.clubColors {
                teamClubColors[team.name] = colors
            }
        }

        let finishedMatches = matches
            .filter { $0.status == "FINISHED" }
            .sorted { ($0.matchday, $0.utcDate) < ($1.matchday, $1.utcDate) }

        for match in finishedMatches {
            let md = match.matchday
            // Use team IDs to get names from our lookup
            guard let home = teamIdToName[match.homeTeam.id],
                  let away = teamIdToName[match.awayTeam.id] else {
                continue // Skip if team not found
            }
            let goalsHome = match.score.fullTime.home ?? 0
            let goalsAway = match.score.fullTime.away ?? 0

            matchdays.insert(md)

            // Initialize data if needed
            for team in [home, away] {
                if stats[team] == nil { stats[team] = [:] }
                if standings[team] == nil { standings[team] = (0, 0, 0) }
                stats[team]![md] = standings[team]! // Save snapshot before update
            }

            // Update result
            if goalsHome > goalsAway {
                standings[home]!.points += 3
            } else if goalsHome < goalsAway {
                standings[away]!.points += 3
            } else {
                standings[home]!.points += 1
                standings[away]!.points += 1
            }

            standings[home]!.gf += goalsHome
            standings[home]!.ga += goalsAway
            standings[away]!.gf += goalsAway
            standings[away]!.ga += goalsHome
        }

        return (stats, Array(teamNames).sorted(), Array(matchdays).sorted(), teamLogos, teamClubColors)
    }
    
    /// Check which seasons are available for a specific league
    func checkAvailableSeasons(for league: League = FootballAPIConfig.defaultLeague) async -> [Int] {
        print("🔍 Checking available seasons for \(league.name)...")
        var availableSeasons: [Int] = []

        for testSeason in FootballAPIConfig.availableSeasons {
            guard let url = URL(string: "\(FootballAPIConfig.baseURL)/competitions/\(league.id)/teams?season=\(testSeason)") else {
                continue
            }

            var request = URLRequest(url: url)
            FootballAPIConfig.headers.forEach { key, value in 
                request.setValue(value, forHTTPHeaderField: key) 
            }

            do {
                let (_, response) = try await session.data(for: request)
                if let httpResponse = response as? HTTPURLResponse {
                    if httpResponse.statusCode == 200 {
                        print("✅ Season \(testSeason): Available")
                        availableSeasons.append(testSeason)
                    } else {
                        print("❌ Season \(testSeason): HTTP \(httpResponse.statusCode)")
                    }
                }
            } catch {
                print("❌ Season \(testSeason): \(error)")
            }
        }
        
        return availableSeasons.sorted(by: >)
    }
    
    // MARK: - Private Methods

    private func handleHTTPResponse(_ response: HTTPURLResponse, season: Int) throws {
        switch response.statusCode {
        case 200:
            return // Success
        case 403:
            print("❌ 403 Forbidden - Season \(season) may not be available or API limit reached")
            throw FootballAPIError.forbidden(season: season)
        case 429:
            print("❌ 429 Too Many Requests - API rate limit exceeded")
            throw FootballAPIError.rateLimitExceeded
        default:
            print("❌ HTTP Error \(response.statusCode)")
            throw FootballAPIError.badServerResponse(statusCode: response.statusCode)
        }
    }
}

// MARK: - Data Manager for SwiftUI Views
@MainActor
class FootballDataManager: ObservableObject {
    @Published var leagueStats: LeagueStats = [:]
    @Published var teams: [String] = []
    @Published var matchdays: [Int] = []
    @Published var teamLogos: [String: String] = [:] // Team name -> Logo URL
    @Published var teamColors: [String: String] = [:] // Team name -> Club colors
    @Published var selectedSeason: Int = 2024 // Current season
    @Published var isLoading = false
    @Published var errorMessage: String?

    private let apiService = FootballAPIService()

    func loadData() async {
        await loadData(for: selectedSeason)
    }

    func loadData(for season: Int) async {
        isLoading = true
        errorMessage = nil
        selectedSeason = season

        // Clear existing data when changing seasons
        leagueStats = [:]
        teams = []
        matchdays = []
        teamLogos = [:]
        teamColors = [:]

        do {
            // Fetch teams and matches separately for the selected season
            let teams = try await apiService.fetchTeams(for: season)
            let matches = try await apiService.fetchMatches(for: season)
            let (stats, teamList, matchdayList, logos, colors) = apiService.processData(matches, teams)

            self.leagueStats = stats
            self.teams = teamList
            self.matchdays = matchdayList
            self.teamLogos = logos
            self.teamColors = colors

            print("Loaded data for season \(season): \(teamList.count) teams, \(matchdayList.count) matchdays")
            print("Team logos: \(logos)")
            print("Team colors: \(colors)")
        } catch {
            print("API failed for season \(season), using sample data: \(error)")

            // Provide more specific error messages
            if let apiError = error as? FootballAPIError {
                self.errorMessage = apiError.errorDescription
            } else {
                self.errorMessage = "Data loading failed: \(error.localizedDescription)"
            }

            loadSampleData()
        }

        isLoading = false
    }

    private func loadSampleData() {
        // Sample data for demonstration
        let sampleTeams = ["Arsenal", "Manchester City", "Liverpool", "Chelsea", "Newcastle United", "Manchester United"]
        let sampleMatchdays = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]

        var sampleStats: LeagueStats = [:]

        for team in sampleTeams {
            sampleStats[team] = [:]
            var points = 0
            for (index, matchday) in sampleMatchdays.enumerated() {
                // Simulate some realistic point progression
                let pointsGained = Int.random(in: 0...3)
                points += pointsGained
                sampleStats[team]![matchday] = (points: points, gf: index * 2, ga: index)
            }
        }

        self.leagueStats = sampleStats
        self.teams = sampleTeams
        self.matchdays = sampleMatchdays
    }

    func getPointsForMatchday(_ matchday: Int) -> [Entry] {
        var entries: [Entry] = []

        for team in teams {
            let points = leagueStats[team]?[matchday]?.points ?? 0
            entries.append(Entry(id: team, value: Double(points)))
        }

        if !teams.isEmpty {
            print("Team names from API: \(teams)")
        }

        return entries.sorted { $0.value > $1.value }
    }
}
