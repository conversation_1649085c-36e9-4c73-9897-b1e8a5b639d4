//
//  DataSyncService.swift
//  PimstatsDashboard
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 15.06.2025.
//

import Foundation

@MainActor
class DataSyncService: ObservableObject {
    
    // MARK: - Published Properties
    @Published var isLoading = false
    @Published var syncProgress: Double = 0.0
    @Published var syncStatus: String = ""
    @Published var errorMessage: String?
    
    // MARK: - Private Properties
    private let apiService = FootballAPIService()
    private let firebaseService = FirebaseService()
    private let githubService = GitHubFootballService()
    private let coordinator: FootballDataCoordinator
    
    init(coordinator: FootballDataCoordinator) {
        self.coordinator = coordinator
    }
    
    // MARK: - Public Methods
    
    /// Sync complete season data from API to Firebase
    func syncSeasonData(for season: Int, league: String = "PL", forceRefresh: Bool = false) async throws {
        isLoading = true
        syncProgress = 0.0
        errorMessage = nil
        
        do {
            // Check if data already exists
            if !forceRefresh {
                let hasData = try await firebaseService.hasDataForSeason(season, league: league)
                if hasData {
                    syncStatus = "Data already exists for season \(season)"
                    isLoading = false
                    return
                }
            }
            
            // Step 1: Fetch teams from selected API (20% progress)
            syncStatus = "Fetching teams for season \(season)..."
            let apiTeams: [TeamDetails]
            if coordinator.dataSource == .footballDataAPI {
                apiTeams = try await apiService.fetchTeams(for: season)
            } else {
                apiTeams = try await githubService.fetchTeams(for: season)
            }
            syncProgress = 0.2
            
            // Step 2: Fetch matches from selected API (40% progress)
            syncStatus = "Fetching matches for season \(season)..."
            let apiMatches: [Match]
            if coordinator.dataSource == .footballDataAPI {
                apiMatches = try await apiService.fetchMatches(for: season)
            } else {
                apiMatches = try await githubService.fetchMatches(for: season)
            }
            syncProgress = 0.4
            
            // Step 3: Process data (50% progress)
            syncStatus = "Processing match data..."
            let (leagueStats, teamNames, matchdays, _, _) = apiService.processData(apiMatches, apiTeams)
            syncProgress = 0.5
            
            // Step 4: Convert to Firebase models (60% progress)
            syncStatus = "Converting to Firebase format..."
            let firebaseTeams = apiTeams.map { FirebaseTeam(from: $0, league: league) }
            let teamSeasonPerformances = convertToTeamSeasonPerformances(
                leagueStats: leagueStats,
                teams: apiTeams,
                season: season,
                league: league,
                matchdays: matchdays
            )
            syncProgress = 0.6
            
            // Step 5: Save teams to Firebase (70% progress)
            syncStatus = "Saving teams to Firebase..."
            try await firebaseService.saveTeams(firebaseTeams)
            syncProgress = 0.7
            
            // Step 6: Save team season performances (90% progress)
            syncStatus = "Saving season performances to Firebase..."
            try await firebaseService.saveTeamSeasonPerformances(teamSeasonPerformances)
            syncProgress = 0.9
            
            // Step 7: Save season summary (100% progress)
            syncStatus = "Saving season summary..."
            let seasonSummary = SeasonSummary(
                season: season,
                league: league,
                totalMatchdays: matchdays.count,
                totalTeams: teamNames.count,
                isComplete: isSeasonComplete(matchdays: matchdays, totalTeams: teamNames.count)
            )
            try await firebaseService.saveSeasonSummary(seasonSummary)
            syncProgress = 1.0
            
            syncStatus = "✅ Successfully synced season \(season) data"
            print("✅ Successfully synced season \(season) with \(teamNames.count) teams and \(matchdays.count) matchdays")
            
        } catch {
            errorMessage = "Failed to sync season \(season): \(error.localizedDescription)"
            syncStatus = "❌ Sync failed"
            print("❌ Sync failed for season \(season): \(error)")
            throw error
        }
        
        isLoading = false
    }
    
    /// Sync multiple seasons
    func syncMultipleSeasons(_ seasons: [Int], league: String = "PL", forceRefresh: Bool = false) async {
        for (index, season) in seasons.enumerated() {
            do {
                syncStatus = "Syncing season \(season) (\(index + 1)/\(seasons.count))..."
                try await syncSeasonData(for: season, league: league, forceRefresh: forceRefresh)
                
                // Add delay between seasons to respect API rate limits
                if index < seasons.count - 1 {
                    syncStatus = "Waiting before next season..."
                    try await Task.sleep(nanoseconds: 2_000_000_000) // 2 seconds
                }
            } catch {
                print("❌ Failed to sync season \(season): \(error)")
                // Continue with next season
            }
        }
        
        syncStatus = "✅ Completed syncing \(seasons.count) seasons"
    }
    
    /// Check available seasons and sync status
    func checkSyncStatus() async -> [Int: Bool] {
        var syncStatus: [Int: Bool] = [:]
        
        for season in FootballAPIConfig.availableSeasons {
            do {
                let hasData = try await firebaseService.hasDataForSeason(season)
                syncStatus[season] = hasData
            } catch {
                print("❌ Error checking sync status for season \(season): \(error)")
                syncStatus[season] = false
            }
        }
        
        return syncStatus
    }
    
    // MARK: - Private Methods
    
    private func convertToTeamSeasonPerformances(
        leagueStats: LeagueStats,
        teams: [TeamDetails],
        season: Int,
        league: String,
        matchdays: [Int]
    ) -> [TeamSeasonPerformance] {
        
        var performances: [TeamSeasonPerformance] = []
        
        // Create team ID to name mapping
        let teamIdToName = Dictionary(uniqueKeysWithValues: teams.map { ($0.id, $0.name) })
        let teamNameToId = Dictionary(uniqueKeysWithValues: teams.map { ($0.name, $0.id) })
        
        for (teamName, teamStats) in leagueStats {
            guard let teamId = teamNameToId[teamName] else { continue }
            
            // Convert team stats to matchday performances
            let matchdayPerformances = matchdays.compactMap { matchday -> MatchdayPerformance? in
                guard let stats = teamStats[matchday] else { return nil }
                return MatchdayPerformance(
                    matchday: matchday,
                    points: stats.points,
                    goalsFor: stats.gf,
                    goalsAgainst: stats.ga
                )
            }
            
            let performance = TeamSeasonPerformance(
                teamId: teamId,
                teamName: teamName,
                season: season,
                league: league,
                matchdayPerformances: matchdayPerformances
            )
            
            performances.append(performance)
        }
        
        return performances
    }
    
    private func isSeasonComplete(matchdays: [Int], totalTeams: Int) -> Bool {
        // A complete season typically has 38 matchdays for Premier League (20 teams)
        // Each team plays every other team twice (home and away)
        let expectedMatchdays = (totalTeams - 1) * 2
        return matchdays.count >= expectedMatchdays
    }
}
