//
//  ContentView.swift
//  PimstatsDashboard
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 15.06.2025.
//

import SwiftUI

struct ContentView: View {
    @StateObject private var coordinator = FootballDataCoordinator()
    @State private var showingDashboard = false

    var body: some View {
        Group {
            if coordinator.isInitialized && !coordinator.syncedSeasons.isEmpty {
                FootballDashboardView()
            } else {
                SystemTestView()
            }
        }
        .onAppear {
            Task {
                await coordinator.initialize()
            }
        }
    }
}

#Preview {
    ContentView()
}
