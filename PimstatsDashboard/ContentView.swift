//
//  ContentView.swift
//  PimstatsDashboard
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 15.06.2025.
//

import SwiftUI

struct ContentView: View {
    @State private var isFirebaseConfigured = false
    @State private var hasCheckedFirebase = false

    var body: some View {
        NavigationView {
            Group {
                if !hasCheckedFirebase {
                    // Loading state
                    VStack {
                        ProgressView()
                        Text("Initializing...")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                } else if isFirebaseConfigured {
                    // Firebase is configured, show setup view
                    FirebaseSetupView()
                } else {
                    // Firebase not configured, show welcome
                    WelcomeView()
                }
            }
        }
        .onAppear {
            checkFirebaseConfiguration()
        }
    }

    private func checkFirebaseConfiguration() {
        // Check if GoogleService-Info.plist exists
        let hasConfigFile = Bundle.main.path(forResource: "GoogleService-Info", ofType: "plist") != nil

        // Check if Firebase is configured
        let isConfigured = hasConfigFile // We'll assume if file exists, Firebase should be configured

        DispatchQueue.main.async {
            self.isFirebaseConfigured = isConfigured
            self.hasCheckedFirebase = true
        }
    }
}

#Preview {
    ContentView()
}
