//
//  ContentView.swift
//  PimstatsDashboard
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 15.06.2025.
//

import SwiftUI

struct ContentView: View {
    @State private var currentView: AppView = .loading
    @State private var showingFirebaseSetup = false

    enum AppView {
        case loading
        case menu
        case welcome
    }

    var body: some View {
        Group {
            switch currentView {
            case .loading:
                VStack {
                    ProgressView()
                    Text("Initializing...")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            case .menu:
                MainMenuView()
            case .welcome:
                WelcomeView()
            }
        }
        .onAppear {
            checkAppState()
        }
        .sheet(isPresented: $showingFirebaseSetup) {
            NavigationView {
                FirebaseSetupView()
            }
        }
    }

    private func checkAppState() {
        // Always show the main menu for easy access to all features
        DispatchQueue.main.async {
            self.currentView = .menu
        }
    }
}

#Preview {
    ContentView()
}
