//
//  PimstatsDashboardApp.swift
//  PimstatsDashboard
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 15.06.2025.
//

import SwiftUI
import FirebaseCore

@main
struct PimstatsDashboardApp: App {

    init() {
        configureFirebase()
    }

    var body: some Scene {
        WindowGroup {
            ContentView()
        }
    }

    private func configureFirebase() {
        // Check if GoogleService-Info.plist exists before configuring Firebase
        guard Bundle.main.path(forResource: "GoogleService-Info", ofType: "plist") != nil else {
            print("⚠️ GoogleService-Info.plist not found. Firebase will not be configured.")
            return
        }

        do {
            FirebaseApp.configure()
            print("✅ Firebase configured successfully")
        } catch {
            print("❌ Firebase configuration failed: \(error)")
        }
    }
}
