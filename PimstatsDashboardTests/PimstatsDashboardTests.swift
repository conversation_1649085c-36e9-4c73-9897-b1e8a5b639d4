//
//  PimstatsDashboardTests.swift
//  PimstatsDashboardTests
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on 15.06.2025.
//

import Testing
@testable import PimstatsDashboard

struct PimstatsDashboardTests {

    @Test func testFootballAPIModels() async throws {
        // Test TeamDetails model
        let teamData = """
        {
            "id": 1,
            "name": "Arsenal",
            "crest": "https://example.com/arsenal.png",
            "clubColors": "Red / White"
        }
        """.data(using: .utf8)!

        let team = try JSONDecoder().decode(TeamDetails.self, from: teamData)
        #expect(team.id == 1)
        #expect(team.name == "Arsenal")
        #expect(team.crest == "https://example.com/arsenal.png")
        #expect(team.clubColors == "Red / White")
    }

    @Test func testFirebaseTeamModel() async throws {
        // Test FirebaseTeam creation from TeamDetails
        let teamDetails = TeamDetails(
            id: 1,
            name: "Arsenal",
            crest: "https://example.com/arsenal.png",
            clubColors: "Red / White"
        )

        let firebaseTeam = FirebaseTeam(from: teamDetails)
        #expect(firebaseTeam.teamId == 1)
        #expect(firebaseTeam.name == "Arsenal")
        #expect(firebaseTeam.crest == "https://example.com/arsenal.png")
        #expect(firebaseTeam.clubColors == "Red / White")
        #expect(firebaseTeam.league == "PL")
    }

    @Test func testMatchdayPerformance() async throws {
        let performance = MatchdayPerformance(
            matchday: 1,
            points: 3,
            goalsFor: 2,
            goalsAgainst: 1,
            position: 1
        )

        #expect(performance.matchday == 1)
        #expect(performance.points == 3)
        #expect(performance.goalsFor == 2)
        #expect(performance.goalsAgainst == 1)
        #expect(performance.goalDifference == 1)
        #expect(performance.position == 1)
    }

    @Test func testTeamSeasonPerformance() async throws {
        let matchdayPerformances = [
            MatchdayPerformance(matchday: 1, points: 3, goalsFor: 2, goalsAgainst: 1),
            MatchdayPerformance(matchday: 2, points: 6, goalsFor: 4, goalsAgainst: 2)
        ]

        let seasonPerformance = TeamSeasonPerformance(
            teamId: 1,
            teamName: "Arsenal",
            season: 2024,
            matchdayPerformances: matchdayPerformances
        )

        #expect(seasonPerformance.teamId == 1)
        #expect(seasonPerformance.teamName == "Arsenal")
        #expect(seasonPerformance.season == 2024)
        #expect(seasonPerformance.totalPoints == 6)
        #expect(seasonPerformance.totalGoalsFor == 4)
        #expect(seasonPerformance.totalGoalsAgainst == 2)
        #expect(seasonPerformance.totalGoalDifference == 2)
    }

    @Test func testFootballAPIConfig() async throws {
        #expect(!FootballAPIConfig.token.isEmpty)
        #expect(FootballAPIConfig.baseURL == "https://api.football-data.org/v4")
        #expect(FootballAPIConfig.league == "PL")
        #expect(!FootballAPIConfig.availableSeasons.isEmpty)
        #expect(FootballAPIConfig.availableSeasons.contains(2024))
    }

    @Test func testFirebaseCollections() async throws {
        #expect(FirebaseCollections.teams == "teams")
        #expect(FirebaseCollections.seasons == "seasons")
        #expect(FirebaseCollections.teamSeasonPerformances == "team_season_performances")
        #expect(FirebaseCollections.seasonSummaries == "season_summaries")
    }

    @Test func testDocumentPaths() async throws {
        let team = FirebaseTeam(teamId: 1, name: "Arsenal")
        #expect(team.documentPath == "teams/PL_1")

        let performance = TeamSeasonPerformance(teamId: 1, teamName: "Arsenal", season: 2024)
        #expect(performance.documentPath == "team_season_performances/PL_2024_1")

        let summary = SeasonSummary(season: 2024)
        #expect(summary.documentPath == "season_summaries/PL_2024")
    }

}
